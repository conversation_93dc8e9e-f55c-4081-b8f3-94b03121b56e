# 问题一解题过程（Q1.md）

## 1. 问题重述
- 场景：一个微基站，50 个 RB。当前用户集合按三类切片（URLLC/eMBB/mMTC），每个用户各自有“一个到达任务”。
- 任务：在三类切片之间划分 50 个 RB，使“总体用户服务质量”最大。
- 片内服务：编号靠前优先（见 `Appendix.md`）。

## 2. 关键参数与公式（引自 Appendix）
- per-user RB 粒度（表 1）：URLLC=10，eMBB=5，mMTC=2。
- 速率：`r = i·b·log(1+γ)`；`i` 为用户占用的 RB 数；`b=360 kHz`；
- 质量函数：
  - URLLC：`y^{URLLC} = { α^L,  L ≤ L_{SLA};  -M^{URLLC},  L > L_{SLA} }`
  - eMBB：基于 `r` 与 `L` 的分段（达到速率 SLA 记满分，未达按 `r/r_SLA`，超时惩罚 `-M`）
  - mMTC：按“接入比例”度量，超时惩罚 `-M`
- 时延：`L = Q + T`（排队 + 传输）。

## 3. 决策变量与约束
- 决策：三类切片的 RB 划分 `x_U, x_E, x_M ∈ ℕ`，且 `x_U + x_E + x_M = 50`。
- 并发能力：
  - `cap_U = ⌊x_U / 10⌋`, `cap_E = ⌊x_E / 5⌋`, `cap_M = ⌊x_M / 2⌋`；
  - 含义：每个 10 ms 子帧内，每类最多并发服务的用户数。
- 片内调度：FIFO（编号靠前优先）。

## 4. 速率与时延计算
- 单用户（在被调度时）
  - `p_rx = 10^{(p_{tx,dBm} - φ_{dB})/10} · |h|^2`（按数据给定/统一功率）
  - `N0 = -174 + 10·log10(i·b) + NF`（dBm）→ 换算到 mW 再参与 SINR；
  - `γ = p_rx / (I+N0)`（Q1 中可令 `I=0` 或按数据给定）；
  - `r = i·b·log(1+γ)`（`i` 为 per-user RB 粒度，该类固定）；
  - 传输时延：`T = data_bits / r`；
- 排队时延 `Q`：按 FIFO，等于用户被首次服务前的等待时间 + 被其他用户占用的累计片长（10 ms 的整数倍）。

## 5. 目标函数
- 最大化全体任务质量之和：`max Σ_j QoS_j(x_U,x_E,x_M)`。

## 6. 求解算法 A：穷举 + 片内仿真（推荐，精确且规模可接受）
- 穷举所有 `(x_U,x_E,x_M)`（`O(50^2)` 个），每个方案执行：
  1) 计算 `cap_*`；
  2) 在若干个 10 ms 子帧内进行片内仿真：
     - 每个子帧，按 `cap_*` 从各类队列头部取若干用户并发服务（按编号顺序）；
     - 为被服务用户累计传输速率与已传数据量；
     - 完成的任务记完成时间，计算 `L` 与 `QoS`；
  3) 累加质量，取最优方案。
- 复杂度：`O(50^2 × (U+E+M) × 子帧数)`，完全可行。

### 伪代码
```pseudo
best_val = -∞; best_x = None
for x_U in 0..50:
  for x_E in 0..(50-x_U):
    x_M = 50 - x_U - x_E
    cap_U = floor(x_U/10); cap_E = floor(x_E/5); cap_M = floor(x_M/2)
    val = SimulateOnce(cap_U, cap_E, cap_M, users, params)
    if val > best_val: best_val, best_x = val, (x_U,x_E,x_M)
return best_x, best_val
```

## 7. 求解算法 B：边际收益贪心（近似）
- 思路：从 `(0,0,0)` 开始，重复 50 次：每次将 1 个 RB 投入到使“质量增益/单位 RB”最大的切片；
- 快速估计增益：
  - 若尚未使 `cap_*` 增加，则增益趋近 0；
  - 当 `x_*` 从 `k·粒度-1 → k·粒度` 跨越时，并发能力 `cap_*` 增 1，增益显著；
  - URLLC 更关注时延降低（使 `L ≤ L_SLA`），eMBB 更关注 `r ≥ r_SLA`；mMTC 关注“接入比例”。

## 8. 灵敏度与鲁棒性
- 观察最优附近的 3×3 网格（对每类 ±1~2 RB），绘制质量热图；
- 检查：
  - 轻微调动 RB 是否导致质量大幅变化（临界点）；
  - 单用户异常（极大/极小 `|h|^2`）是否支配结果。

## 9. 输出与复现
- 输出：最佳 `(x_U,x_E,x_M)`、每类/整体质量、满足 SLA 的比例、平均/95% 时延；
- 附：关键中间量（`cap_*`，各类完成任务数、未完成/超时数）。

## 10. 说明
- 本问未引入跨小区干扰与功率优化；若附件给定固定发射功率，直接代入；
- 若需考虑 10 ms 片内更细粒度的调度（多子帧），仿真引擎同样适用。
