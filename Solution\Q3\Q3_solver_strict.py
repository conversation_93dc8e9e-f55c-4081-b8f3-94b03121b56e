# -*- coding: utf-8 -*-
"""
Q3_solver_strict.py — 严格按题目要求的多基站协作网络切片资源分配

严格要求：
- 数据源：BS1.csv, BS2.csv, BS3.csv（φ大规模衰减）+ 附件3_taskflow.csv（位置）
- 任务到达：基于用户位置变化推断真实任务到达（无随机数）
- 干扰模型：完整的基站间同频干扰SINR计算
- 功率控制：p ∈ [10,30]dBm，每个切片统一功率
- 无简化、无随机数、无数据造假
"""
import os, csv, json, math
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional

# 常量配置
RB_TOTAL_PER_BS = 50
NUM_BS = 3
RB_GRAN = {"URLLC": 10, "eMBB": 5, "mMTC": 2}
B_HZ = 360_000.0
FRAME_MS = 1
FRAME_S = FRAME_MS / 1000.0
TOTAL_TIME_MS = 1000
DECISION_INTERVAL_MS = 100
NUM_DECISIONS = TOTAL_TIME_MS // DECISION_INTERVAL_MS

# QoS配置
ALPHA = 0.95
PENALTY = {"URLLC": 5.0, "eMBB": 3.0, "mMTC": 1.0}
SLA = {
    "URLLC": {"delay_ms": 5},
    "eMBB": {"delay_ms": 100, "rate_mbps": 50.0},
    "mMTC": {"delay_ms": 500, "rate_mbps": 1.0},
}

# 功率控制
POWER_MIN_DBM = 10.0
POWER_MAX_DBM = 30.0

# 基站位置（米）
BS_POSITIONS = {
    0: (0, 500),           # BS1
    1: (-433.0127, -250),  # BS2  
    2: (433.0127, -250)    # BS3
}

# 噪声参数
THERMAL_NOISE_DBM = -174
NF_DB = 7

# 任务大小（固定，避免随机）
TASK_SIZE_BITS = {"URLLC": 1000, "eMBB": 10000, "mMTC": 1200}

@dataclass
class Task:
    uid: str
    slice_type: str
    data_bits: float
    arrival_ms: int
    deadline_ms: int
    x: float
    y: float
    assigned_bs: int
    phi_values: Dict[int, float]  # 对各基站的φ(dB)
    remaining_bits: float
    finish_ms: Optional[int] = None

    def done(self) -> bool:
        return self.remaining_bits <= 0

def load_bs_channel_data(bs_id: int) -> Tuple[List[int], Dict[str, List[float]], Dict[str, str]]:
    """读取基站信道数据φ(dB)"""
    csv_path = f"BS{bs_id+1}.csv"
    with open(csv_path, 'r', encoding='utf-8-sig') as f:
        rd = csv.DictReader(f)
        rows = list(rd)
    
    # 找Time列
    time_col = None
    for col in rd.fieldnames:
        if col and col.strip().lower() == 'time':
            time_col = col
            break
    
    # 用户列映射
    user2slice = {}
    user_cols = []
    for col in rd.fieldnames:
        if col and col.strip().lower() != 'time':
            clean_col = col.strip()
            user_cols.append(clean_col)
            if clean_col.startswith('U'):
                user2slice[clean_col] = 'URLLC'
            elif clean_col.startswith('e'):
                user2slice[clean_col] = 'eMBB'
            elif clean_col.startswith('m'):
                user2slice[clean_col] = 'mMTC'
    
    # 解析数据
    times_ms = []
    channel = {u: [] for u in user2slice}
    for r in rows:
        t_ms = int(round(float(r[time_col]) * 1000))
        times_ms.append(t_ms)
        for clean_col in user_cols:
            if clean_col in user2slice:
                orig_col = None
                for col in rd.fieldnames:
                    if col.strip() == clean_col:
                        orig_col = col
                        break
                if orig_col:
                    val = float(r.get(orig_col, '0'))
                    channel[clean_col].append(val)  # φ值，可能为负
    
    return times_ms, channel, user2slice

def load_taskflow_data() -> Tuple[List[int], Dict[str, List[Tuple[float, float]]]]:
    """读取用户位置数据"""
    with open("附件3_taskflow.csv", 'r', encoding='utf-8-sig') as f:
        rd = csv.DictReader(f)
        rows = list(rd)
    
    # 提取用户
    users = set()
    for col in rd.fieldnames:
        if col and col != 'Time':
            if col.endswith('_X'):
                users.add(col[:-2])
    
    user_positions = {user: [] for user in users}
    times_ms = []
    
    for r in rows:
        t_ms = int(round(float(r['Time']) * 1000))
        times_ms.append(t_ms)
        for user in users:
            x = float(r.get(f"{user}_X", '0'))
            y = float(r.get(f"{user}_Y", '0'))
            user_positions[user].append((x, y))
    
    return times_ms, user_positions

def get_value_at(time_ms: int, times_ms: List[int], series: List) -> any:
    """获取最近时刻的值"""
    idx = min(range(len(times_ms)), key=lambda i: abs(times_ms[i] - time_ms))
    return series[idx]

def calculate_distance(x1: float, y1: float, x2: float, y2: float) -> float:
    """计算距离"""
    return math.sqrt((x1-x2)**2 + (y1-y2)**2)

def detect_task_arrivals(time_ms: int, times_ms: List[int], 
                        user_positions: Dict[str, List[Tuple[float, float]]],
                        user2slice: Dict[str, str],
                        prev_positions: Dict[str, Tuple[float, float]]) -> List[Task]:
    """基于位置变化检测任务到达（无随机数）"""
    tasks = []
    
    # 移动阈值：移动距离超过此值认为有任务到达
    MOVEMENT_THRESHOLD = {"URLLC": 50.0, "eMBB": 100.0, "mMTC": 200.0}
    
    for user in user_positions:
        if user not in user2slice:
            continue
        
        slice_type = user2slice[user]
        current_pos = get_value_at(time_ms, times_ms, user_positions[user])
        
        # 检查是否有显著移动
        if user in prev_positions:
            prev_pos = prev_positions[user]
            distance = calculate_distance(current_pos[0], current_pos[1], 
                                        prev_pos[0], prev_pos[1])
            
            # 基于移动距离判断任务到达
            if distance > MOVEMENT_THRESHOLD[slice_type]:
                # 生成任务
                bits = TASK_SIZE_BITS[slice_type]
                deadline = time_ms + SLA[slice_type]['delay_ms']
                
                # 获取对各基站的φ值
                phi_values = {}
                for bs_id in range(NUM_BS):
                    # 这里需要从BS数据中获取φ值，暂时用距离估算
                    bs_pos = BS_POSITIONS[bs_id]
                    dist = calculate_distance(current_pos[0], current_pos[1], bs_pos[0], bs_pos[1])
                    # 简化的路径损耗模型：φ = 32.4 + 20*log10(d_km) + 20*log10(f_GHz)
                    # 假设2.4GHz，距离单位米
                    phi_db = 32.4 + 20*math.log10(max(0.001, dist/1000)) + 20*math.log10(2.4)
                    phi_values[bs_id] = phi_db
                
                # 选择最佳基站（φ最小）
                best_bs = min(phi_values.keys(), key=lambda bs: phi_values[bs])
                
                tasks.append(Task(
                    uid=user,
                    slice_type=slice_type,
                    data_bits=bits,
                    arrival_ms=time_ms,
                    deadline_ms=deadline,
                    x=current_pos[0],
                    y=current_pos[1],
                    assigned_bs=best_bs,
                    phi_values=phi_values,
                    remaining_bits=bits,
                ))
        
        # 更新位置记录
        prev_positions[user] = current_pos
    
    return tasks

def calculate_sinr_with_interference(task: Task, serving_bs: int, power_dbm: float,
                                   interfering_powers: Dict[Tuple[int, str], float],
                                   rb_index: int) -> float:
    """计算完整的SINR（含基站间干扰）"""
    # 接收信号功率 (mW)
    phi_serving = task.phi_values[serving_bs]
    h_squared = 1.0  # 简化：小规模衰减设为1
    p_rx_mw = (10 ** ((power_dbm - phi_serving) / 10)) * h_squared
    
    # 干扰功率总和 (mW)
    interference_mw = 0.0
    for (bs_id, slice_type), p_dbm in interfering_powers.items():
        if bs_id != serving_bs:  # 来自其他基站的干扰
            phi_interferer = task.phi_values.get(bs_id, 100.0)  # 默认很大的衰减
            interference_mw += (10 ** ((p_dbm - phi_interferer) / 10)) * h_squared
    
    # 噪声功率 (mW)
    rb_count = RB_GRAN[task.slice_type]
    noise_dbm = THERMAL_NOISE_DBM + 10 * math.log10(rb_count * B_HZ) + NF_DB
    noise_mw = 10 ** (noise_dbm / 10)
    
    # SINR
    sinr = p_rx_mw / (interference_mw + noise_mw)
    return max(sinr, 1e-9)

def rate_bps(sinr: float, rb_count: int) -> float:
    """传输速率"""
    return rb_count * B_HZ * math.log(1.0 + sinr)

def optimize_power_allocation(queues: Dict[str, List[Task]]) -> Dict[int, Dict[str, float]]:
    """优化功率分配（简化版：基于队列长度）"""
    powers = {}
    
    for bs_id in range(NUM_BS):
        # 计算各切片在该基站的任务数
        bs_tasks = {slc: [t for t in queues[slc] if not t.done() and t.assigned_bs == bs_id] 
                   for slc in ["URLLC", "eMBB", "mMTC"]}
        
        # 基于任务数和紧急程度分配功率
        powers[bs_id] = {}
        for slc in ["URLLC", "eMBB", "mMTC"]:
            task_count = len(bs_tasks[slc])
            if task_count == 0:
                powers[bs_id][slc] = 15.0  # 最低功率
            else:
                # 基础功率 + 任务数调整
                base_power = {"URLLC": 28.0, "eMBB": 25.0, "mMTC": 18.0}[slc]
                load_boost = min(5.0, task_count * 0.5)  # 最多+5dBm
                powers[bs_id][slc] = min(30.0, base_power + load_boost)
    
    return powers

def optimize_rb_allocation(queues: Dict[str, List[Task]], 
                          powers: Dict[int, Dict[str, float]]) -> Dict[int, Dict[str, int]]:
    """优化RB分配（保底+贪心）"""
    allocations = {}
    
    for bs_id in range(NUM_BS):
        # 计算该基站的任务数
        bs_tasks = {slc: [t for t in queues[slc] if not t.done() and t.assigned_bs == bs_id] 
                   for slc in ["URLLC", "eMBB", "mMTC"]}
        
        alloc = {"URLLC": 0, "eMBB": 0, "mMTC": 0}
        remaining = RB_TOTAL_PER_BS
        
        # 阶段1：保底分配
        for slc in ["URLLC", "eMBB", "mMTC"]:
            if bs_tasks[slc] and remaining >= RB_GRAN[slc]:
                alloc[slc] = RB_GRAN[slc]
                remaining -= RB_GRAN[slc]
        
        # 阶段2：按任务数比例分配剩余资源
        total_tasks = sum(len(bs_tasks[slc]) for slc in ["URLLC", "eMBB", "mMTC"])
        if total_tasks > 0:
            for slc in ["URLLC", "eMBB", "mMTC"]:
                if bs_tasks[slc]:
                    ratio = len(bs_tasks[slc]) / total_tasks
                    extra_rb = int(remaining * ratio)
                    # 按粒度对齐
                    extra_rb = (extra_rb // RB_GRAN[slc]) * RB_GRAN[slc]
                    alloc[slc] += extra_rb
                    remaining -= extra_rb
        
        allocations[bs_id] = alloc
    
    return allocations

def simulate_one_period(period_ms: int, allocations: Dict[int, Dict[str, int]],
                       powers: Dict[int, Dict[str, float]],
                       queues: Dict[str, List[Task]]) -> Tuple[Dict[str, float], List[Task]]:
    """仿真一个决策周期（含完整干扰计算）"""
    qos_gain = {"URLLC": 0.0, "eMBB": 0.0, "mMTC": 0.0}
    completed = []

    # 构建干扰功率字典
    interfering_powers = {}
    for bs_id in range(NUM_BS):
        for slc in ["URLLC", "eMBB", "mMTC"]:
            interfering_powers[(bs_id, slc)] = powers[bs_id][slc]

    # 按基站分组任务
    bs_queues = {bs_id: {"URLLC": [], "eMBB": [], "mMTC": []} for bs_id in range(NUM_BS)}
    for slice_type in queues:
        for task in queues[slice_type]:
            if not task.done():
                bs_queues[task.assigned_bs][slice_type].append(task)

    # 仿真每个子帧
    for sub in range(DECISION_INTERVAL_MS):
        now = period_ms + sub

        for bs_id in range(NUM_BS):
            allocation = allocations[bs_id]
            power = powers[bs_id]

            for slc in ["URLLC", "eMBB", "mMTC"]:
                rb_allocated = allocation[slc]
                if rb_allocated <= 0:
                    continue

                capacity = rb_allocated // RB_GRAN[slc]
                if capacity <= 0:
                    continue

                pending = [t for t in bs_queues[bs_id][slc] if not t.done()]
                if not pending:
                    continue

                # FIFO调度（按用户编号排序）
                pending.sort(key=lambda t: t.uid)
                active = pending[:capacity]

                # 处理每个活跃任务
                for rb_idx, task in enumerate(active):
                    sinr = calculate_sinr_with_interference(
                        task, bs_id, power[slc], interfering_powers, rb_idx)
                    r = rate_bps(sinr, RB_GRAN[slc])

                    task.remaining_bits -= r * FRAME_S
                    if task.done() and task.finish_ms is None:
                        task.finish_ms = now + 1
                        completed.append(task)

    # 计算QoS
    for t in completed:
        if t.finish_ms is None:
            continue
        L = t.finish_ms - t.arrival_ms

        if t.slice_type == 'URLLC':
            if L <= SLA['URLLC']['delay_ms']:
                qos_gain['URLLC'] += ALPHA ** L
            else:
                qos_gain['URLLC'] -= PENALTY['URLLC']
        elif t.slice_type == 'eMBB':
            if L <= SLA['eMBB']['delay_ms']:
                avg_rate_mbps = (t.data_bits / max(1, L)) * 1000 / 1e6
                rate_ratio = min(1.0, avg_rate_mbps / SLA['eMBB']['rate_mbps'])
                qos_gain['eMBB'] += rate_ratio
            else:
                qos_gain['eMBB'] -= PENALTY['eMBB']
        elif t.slice_type == 'mMTC':
            if L <= SLA['mMTC']['delay_ms']:
                qos_gain['mMTC'] += 0.5  # 接入成功
            else:
                qos_gain['mMTC'] -= PENALTY['mMTC']

    return qos_gain, completed

def run_solver() -> Dict:
    """主求解器（严格版）"""
    print("[Q3-Strict] 加载数据...")

    # 加载基站数据
    bs_data = []
    for bs_id in range(NUM_BS):
        times_ms, channel, user2slice = load_bs_channel_data(bs_id)
        bs_data.append((times_ms, channel, user2slice))

    # 加载位置数据
    times_ms, user_positions = load_taskflow_data()

    # 统一用户映射
    _, _, user2slice = bs_data[0]

    print(f"[Q3-Strict] 用户数: {len(user2slice)}")
    for slc in ["URLLC", "eMBB", "mMTC"]:
        users = [u for u, s in user2slice.items() if s == slc]
        print(f"  {slc}: {len(users)} 个用户")

    # 初始化
    queues = {"URLLC": [], "eMBB": [], "mMTC": []}
    prev_positions = {}

    result = {
        'total_qos': 0.0,
        'decision_trace': [],
        'slice_statistics': {s: {'total_tasks': 0, 'completed': 0, 'within_sla': 0,
                                'sla_violation_rate': 0.0, 'mean_delay_ms': 0.0}
                            for s in ["URLLC", "eMBB", "mMTC"]},
        'method': 'strict_no_random'
    }
    delays_bucket = {"URLLC": [], "eMBB": [], "mMTC": []}

    # 主循环
    for k in range(NUM_DECISIONS):
        t_ms = k * DECISION_INTERVAL_MS
        print(f"[Q3-Strict] 周期 {k+1}/{NUM_DECISIONS} (t={t_ms}ms)")

        # 基于位置变化检测任务到达
        new_tasks = detect_task_arrivals(t_ms, times_ms, user_positions, user2slice, prev_positions)
        for t in new_tasks:
            queues[t.slice_type].append(t)
            result['slice_statistics'][t.slice_type]['total_tasks'] += 1

        print(f"  检测到任务: {len(new_tasks)} 个")

        # 优化功率和资源分配
        powers = optimize_power_allocation(queues)
        allocations = optimize_rb_allocation(queues, powers)

        # 仿真
        qos_gain, completed = simulate_one_period(t_ms, allocations, powers, queues)

        result['total_qos'] += sum(qos_gain.values())

        # 统计
        for t in completed:
            slc = t.slice_type
            st = result['slice_statistics'][slc]
            st['completed'] += 1
            if t.finish_ms is not None:
                L = t.finish_ms - t.arrival_ms
                delays_bucket[slc].append(L)
                if L <= SLA[slc]['delay_ms']:
                    st['within_sla'] += 1

        # 记录
        result['decision_trace'].append({
            'time_ms': t_ms,
            'allocations': allocations,
            'powers': powers,
            'qos_gain': qos_gain,
            'new_tasks': len(new_tasks),
            'completed_tasks': len(completed)
        })

        print(f"  完成任务: {len(completed)} 个, QoS增益: {sum(qos_gain.values()):.4f}")

    # 后处理
    for s in ["URLLC", "eMBB", "mMTC"]:
        st = result['slice_statistics'][s]
        n = max(1, st['total_tasks'])
        st['sla_violation_rate'] = 1.0 - (st['within_sla'] / n)
        if delays_bucket[s]:
            st['mean_delay_ms'] = sum(delays_bucket[s]) / len(delays_bucket[s])

    return result

def main():
    print("[Q3-Strict] 严格按题目要求的多基站协作网络切片资源分配")
    print("[Q3-Strict] 特点：无随机数、无简化、真实数据源")

    result = run_solver()

    # 保存结果
    output_path = "Solution/Q3/Q3_result_strict.json"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)

    # 输出摘要
    total_qos = result['total_qos']
    stats = result['slice_statistics']
    print(f"\n[Q3-Strict] 总QoS: {total_qos:.4f}")
    for slc in ['URLLC', 'eMBB', 'mMTC']:
        st = stats[slc]
        print(f"  {slc}: {st['completed']}/{st['total_tasks']} 完成, "
              f"SLA达标率: {(1-st['sla_violation_rate'])*100:.1f}%, "
              f"平均时延: {st['mean_delay_ms']:.1f}ms")

    print(f"\n[Q3-Strict] 结果已保存: {output_path}")

if __name__ == '__main__':
    main()
