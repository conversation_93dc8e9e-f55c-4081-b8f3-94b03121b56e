# 问题三解题过程（Q3.md）

## 1. 问题重述
- 场景：多个微基站（共用同一频谱，频率复用=1），仅微基站之间存在同频干扰；每个微基站拥有 50 个 RB。
- 决策周期：与题面一致，每 100 ms 决策一次（总 1000 ms，10 次），周期内再以 10 ms 子帧进行片内仿真。
- 任务与信道：附件 3 给出整个期间内所有用户的任务到达与与各基站间的信道信息（含大/小尺度衰落）。
- 决策内容（每个基站、每次决策）：
  1) 切片 RB 划分 `x_{b,U}, x_{b,E}, x_{b,M}`，满足 `x_{b,U}+x_{b,E}+x_{b,M}=50`；
  2) 每切片统一的发射功率 `p_{b,U}, p_{b,E}, p_{b,M}`（dBm），范围 `10–30 dBm`（见 `Topic.md`）。
- 片内：每类用户占用固定 RB 粒度（URLLC=10，eMBB=5，mMTC=2）；每 10 ms 子帧 FIFO 调度；同用户多 RB 相邻、同切片 RB 相邻（OFDMA 相邻性，见 `Appendix.md`）。
- 用户归属：本问不决策用户接入基站，按附件（或“最近微基站”）给定。
- 目标：最大化全体用户在 1000 ms 内的总体服务质量（QoS）之和。

## 2. 系统建模
- 基站集合：`b ∈ B`（微基站）。每个基站每周期可分配 50 个 RB。
- 时间：`t ∈ {0,100,...,900} ms`；每周期含 10 个 `Δ=10 ms` 子帧。
- 切片并发能力：`cap_{b,U}(t)=⌊x_{b,U}(t)/10⌋`，`cap_{b,E}(t)=⌊x_{b,E}(t)/5⌋`，`cap_{b,M}(t)=⌊x_{b,M}(t)/2⌋`。
- 干扰与 SINR：对由基站 `b` 在切片 `s∈{U,E,M}` 上、RB 索引 `f` 服务的用户 `k`，
  - 设 `p_{b,s}` 为 dBm，换算到瓦特：`P_{b,s}(W) = 10^{(p_{b,s}-30)/10}`；
  - 接收信号功率：`p_rx = 10^{(p_{b,s}-φ_{b,k})/10} · |h_{b,k}|^2`（mW，见 `Appendix.md`）；
  - 干扰：来自所有 `u≠b` 且在同一 RB 索引 `f` 上发送的功率叠加：`I = Σ_{u≠b} 10^{(p_{u,s_u}-φ_{u,k})/10} · |h_{u,k}|^2`；
  - 噪声：`N0 = -174 + 10·log10(i·b) + NF`（dBm，转线性后参与计算），`i` 为该类用户的 per-user RB 数；
  - `γ = p_rx / (I + N0)`，`r = i·b·log(1+γ)`。
- OFDMA 相邻性：同一切片 `s` 在基站 `b` 的 `x_{b,s}` 个 RB 在频域上连续。每个被服务用户在该切片占用其固定粒度、且需要在该连续块内连续索引的 RB。
- 片内调度：FIFO，优先服务编号靠前；每个子帧每类最多并发 `cap_{b,s}` 个用户。
- 质量函数与 SLA：与 `Appendix.md` 第 3 节一致（URLLC 时延优先；eMBB 速率阈值与超时处理；mMTC 接入比例与超时惩罚）。

## 3. 决策变量与约束（每个周期 t）
- 变量：`X(t) = {x_{b,s}(t)}`, `P(t) = {p_{b,s}(t)}`。
- 约束：
  - 资源约束：`∀b, Σ_s x_{b,s}(t) = 50`，且 `x_{b,s}(t) ∈ ℕ`；
  - 功率约束：`∀b,s, 10 ≤ p_{b,s}(t) ≤ 30`（dBm）；
  - OFDMA 相邻性：`x_{b,s}` 对应的 RB 区间连续且不同切片区间不重叠；
  - 片内并发与 FIFO 调度遵循粒度与用户顺序。
- 目标：最大化 10 个周期内所有用户 QoS 之和 `Σ_t Σ_k QoS_k(t)`。

## 4. 求解方法一：交替优化（RB 划分 ↔ 功率控制，推荐）
- 思路：在每个决策时刻 `t`，通过外层滚动（MPC/DPP）与内层交替（X 与 P）相结合：
  1) 固定 `X` 优化 `P`（功率控制）：在功率约束内提升“加权效用”（基于队列紧迫度/接近 SLA 权重）；
  2) 固定 `P` 优化 `X`（RB 划分）：在考虑干扰（由当前 `P` 预测）的情况下进行多基站协同的 RB 切片划分；
  3) 在 `t` 上迭代 2~5 轮或至收敛，执行得到的 `X(t),P(t)`，推进 10 个子帧仿真；滚动到 `t+100`。
- 功率子问题（固定 `X`）：
  - 目标：最大化 `Σ_{b,s} w_{b,s} · U_{b,s}(P)`，其中权重 `w` 由 DPP/MPC 窗口内的紧迫度给出，`U` 可取切片内用户的加权和速率或 QoS 近似；
  - 方法：
    - 梯度投影/爬山（对 dBm 进行裁剪至 [10,30]）；
    - 或 WMMSE 近似/标准干扰函数法（Yates）进行分布式更新。
- RB 子问题（固定 `P`）：
  - 目标：最大化 `Σ_{b,s} w_{b,s} · U_{b,s}(X)`，在 `Σ_s x_{b,s}=50`、相邻性与整数约束下；
  - 方法：
    - 每基站局部贪心/动态规划：由于相邻性，等价于把 50 个 RB 划分成最多 3 个连续段；
    - 多基站协调：通过“干扰价格”或在 MPC 窗口中枚举少量候选切分（例如每类以其粒度为步长），选取全局增益更大的组合。

### 伪代码（单个决策周期 t）
```pseudo
input: queues, channels, prev X,P
X ← warm-start from prev; P ← clip(prev P)
for iter = 1..K (K=3~5):
  # Power update
  for (b,s) in B×{U,E,M}:
    g = ∂Utility/∂p_{b,s} (via SINR chain rule)
    p_{b,s} ← clip_{[10,30]}(p_{b,s} + η·g)
  # RB update with adjacency
  for b in B:
    candidates = all 1D partitions of 50 into 3 contiguous blocks on step {10,5,2}
    X_b ← argmax_{cand∈candidates} Utility_b(cand | P)
execute X(t)=X, P(t)=P; Simulate10Subframes(X,P); t←t+100
```

## 5. 求解方法二：DPP（在线） + 分布式功率控制
- DPP/Dynamic Weight：每周期根据队列与 SLA 计算切片权重 `w_{b,s}`，逐个 RB 赋给“单位增益最大”的 `(b,s)`，同时周期内穿插少量步长的功率爬山更新。
- 特点：实现简洁、鲁棒，适合大规模；性能略低于交替优化但计算可控。

## 6. 片内调度与质量记账
- 片内：每子帧每类最多并发 `cap_{b,s}` 个用户，FIFO；完成任务记录其 `L=Q+T` 并依切片 QoS 计分；超时记惩罚 `-M`；mMTC 接入比例按周期统计。

## 7. 复杂度与实现建议
- 交替优化：`O(K × (|B|×候选切分 + 功率更新开销))`，K≈3~5；
- 建议：
  - 复用 Q2 的仿真引擎（10 ms 子帧）；
  - 以“粒度步长”构造有限候选切分（大幅降复杂度）；
  - 功率更新可分布式并行。

## 8. 输出与分析
- 输出：每周期各基站 `(x_{b,U},x_{b,E},x_{b,M})` 与 `(p_{b,U},p_{b,E},p_{b,M})`，各类/整体 QoS，SLA 违约，时延统计；
- 可视化：
  - 干扰热图（基站-基站对之间的平均干扰强度）；
  - 不同方法（交替优化、DPP）的 QoS 对比与收敛曲线；
  - 功率分配随时间的曲线与分布。

## 9. 备注
- 单位换算：功率 dBm→W：`P(W)=10^{(p(dBm)-30)/10}`；噪声 dBm 亦需转线性后参与 `γ`；
- 相邻性：同切片/同用户 RB 相邻（`Appendix.md` 第 2 节）。
- 功率范围：微基站每切片 10–30 dBm（`Topic.md`）。
