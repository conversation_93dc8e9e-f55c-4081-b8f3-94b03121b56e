{"total_qos": 29.006532392223416, "decision_trace": [{"time_ms": 0, "allocations": {"0": {"URLLC": 0, "eMBB": 25, "mMTC": 12}, "1": {"URLLC": 0, "eMBB": 0, "mMTC": 50}, "2": {"URLLC": 0, "eMBB": 5, "mMTC": 40}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 29.0, "mMTC": 21.2}, "1": {"URLLC": 15.0, "eMBB": 15.0, "mMTC": 23.0}, "2": {"URLLC": 15.0, "eMBB": 25.8, "mMTC": 23.0}}, "qos_gain": {"URLLC": 0.0, "eMBB": 0.1382936507936508, "mMTC": 2.2000000000000006}, "new_tasks": 28, "completed_tasks": 28}, {"time_ms": 100, "allocations": {"0": {"URLLC": 10, "eMBB": 10, "mMTC": 18}, "1": {"URLLC": 0, "eMBB": 10, "mMTC": 34}, "2": {"URLLC": 0, "eMBB": 10, "mMTC": 34}}, "powers": {"0": {"URLLC": 28.8, "eMBB": 26.6, "mMTC": 21.2}, "1": {"URLLC": 15.0, "eMBB": 26.6, "mMTC": 23.0}, "2": {"URLLC": 15.0, "eMBB": 25.8, "mMTC": 23.0}}, "qos_gain": {"URLLC": 0.95, "eMBB": 0.04774530429282513, "mMTC": 2.2000000000000006}, "new_tasks": 28, "completed_tasks": 28}, {"time_ms": 200, "allocations": {"0": {"URLLC": 0, "eMBB": 20, "mMTC": 18}, "1": {"URLLC": 10, "eMBB": 10, "mMTC": 22}, "2": {"URLLC": 0, "eMBB": 10, "mMTC": 32}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 27.4, "mMTC": 22.0}, "1": {"URLLC": 28.8, "eMBB": 26.6, "mMTC": 23.0}, "2": {"URLLC": 15.0, "eMBB": 25.8, "mMTC": 22.0}}, "qos_gain": {"URLLC": 0.95, "eMBB": 0.0611388393741335, "mMTC": 1.8000000000000005}, "new_tasks": 25, "completed_tasks": 25}, {"time_ms": 300, "allocations": {"0": {"URLLC": 0, "eMBB": 20, "mMTC": 18}, "1": {"URLLC": 0, "eMBB": 20, "mMTC": 18}, "2": {"URLLC": 0, "eMBB": 0, "mMTC": 50}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 27.4, "mMTC": 22.0}, "1": {"URLLC": 15.0, "eMBB": 29.0, "mMTC": 23.0}, "2": {"URLLC": 15.0, "eMBB": 15.0, "mMTC": 23.0}}, "qos_gain": {"URLLC": 0.0, "eMBB": 0.13648591174906965, "mMTC": 2.2000000000000006}, "new_tasks": 30, "completed_tasks": 30}, {"time_ms": 400, "allocations": {"0": {"URLLC": 0, "eMBB": 0, "mMTC": 50}, "1": {"URLLC": 0, "eMBB": 15, "mMTC": 24}, "2": {"URLLC": 10, "eMBB": 10, "mMTC": 20}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 15.0, "mMTC": 22.0}, "1": {"URLLC": 15.0, "eMBB": 27.4, "mMTC": 22.8}, "2": {"URLLC": 28.8, "eMBB": 27.4, "mMTC": 23.0}}, "qos_gain": {"URLLC": 0.95, "eMBB": 0.07557682083997874, "mMTC": 1.9000000000000006}, "new_tasks": 26, "completed_tasks": 26}, {"time_ms": 500, "allocations": {"0": {"URLLC": 0, "eMBB": 20, "mMTC": 16}, "1": {"URLLC": 0, "eMBB": 0, "mMTC": 50}, "2": {"URLLC": 0, "eMBB": 15, "mMTC": 26}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 29.0, "mMTC": 22.8}, "1": {"URLLC": 15.0, "eMBB": 15.0, "mMTC": 23.0}, "2": {"URLLC": 15.0, "eMBB": 26.6, "mMTC": 22.8}}, "qos_gain": {"URLLC": 0.0, "eMBB": 0.1285294117647059, "mMTC": 2.1000000000000005}, "new_tasks": 28, "completed_tasks": 28}, {"time_ms": 600, "allocations": {"0": {"URLLC": 10, "eMBB": 5, "mMTC": 28}, "1": {"URLLC": 0, "eMBB": 15, "mMTC": 26}, "2": {"URLLC": 0, "eMBB": 10, "mMTC": 32}}, "powers": {"0": {"URLLC": 28.8, "eMBB": 25.8, "mMTC": 23.0}, "1": {"URLLC": 15.0, "eMBB": 26.6, "mMTC": 22.8}, "2": {"URLLC": 15.0, "eMBB": 26.6, "mMTC": 23.0}}, "qos_gain": {"URLLC": 0.95, "eMBB": 0.04164558157545535, "mMTC": 2.3000000000000007}, "new_tasks": 29, "completed_tasks": 29}, {"time_ms": 700, "allocations": {"0": {"URLLC": 0, "eMBB": 15, "mMTC": 24}, "1": {"URLLC": 10, "eMBB": 5, "mMTC": 26}, "2": {"URLLC": 0, "eMBB": 15, "mMTC": 24}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 28.2, "mMTC": 23.0}, "1": {"URLLC": 29.6, "eMBB": 25.8, "mMTC": 23.0}, "2": {"URLLC": 15.0, "eMBB": 26.6, "mMTC": 22.0}}, "qos_gain": {"URLLC": 1.8525, "eMBB": 0.058806456701193555, "mMTC": 2.500000000000001}, "new_tasks": 34, "completed_tasks": 34}, {"time_ms": 800, "allocations": {"0": {"URLLC": 0, "eMBB": 25, "mMTC": 12}, "1": {"URLLC": 0, "eMBB": 10, "mMTC": 34}, "2": {"URLLC": 0, "eMBB": 15, "mMTC": 24}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 29.0, "mMTC": 22.0}, "1": {"URLLC": 15.0, "eMBB": 26.6, "mMTC": 23.0}, "2": {"URLLC": 15.0, "eMBB": 26.6, "mMTC": 21.2}}, "qos_gain": {"URLLC": 0.0, "eMBB": 0.13742608268924061, "mMTC": 2.0000000000000004}, "new_tasks": 29, "completed_tasks": 29}, {"time_ms": 900, "allocations": {"0": {"URLLC": 0, "eMBB": 15, "mMTC": 24}, "1": {"URLLC": 0, "eMBB": 10, "mMTC": 34}, "2": {"URLLC": 10, "eMBB": 10, "mMTC": 20}}, "powers": {"0": {"URLLC": 15.0, "eMBB": 27.4, "mMTC": 22.8}, "1": {"URLLC": 15.0, "eMBB": 25.8, "mMTC": 23.0}, "2": {"URLLC": 28.8, "eMBB": 28.2, "mMTC": 23.0}}, "qos_gain": {"URLLC": 0.95, "eMBB": 0.07838433244315596, "mMTC": 2.3000000000000007}, "new_tasks": 32, "completed_tasks": 32}], "slice_statistics": {"URLLC": {"total_tasks": 7, "completed": 7, "within_sla": 7, "sla_violation_rate": 0.0, "mean_delay_ms": 1.1428571428571428}, "eMBB": {"total_tasks": 67, "completed": 67, "within_sla": 67, "sla_violation_rate": 0.0, "mean_delay_ms": 17.62686567164179}, "mMTC": {"total_tasks": 215, "completed": 215, "within_sla": 215, "sla_violation_rate": 0.0, "mean_delay_ms": 11.427906976744186}}, "data_source": "BS1-3.csv + 附件3_taskflow.csv + 附件3_user_taskflow.csv", "method": "real_arrival_data_based"}