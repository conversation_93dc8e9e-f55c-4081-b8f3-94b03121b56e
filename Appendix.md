## 1. 信号传输模型

在用户$k$和基站$n$之间进行信号传输时,由基站发出的信号在用户接收前会进行一定的衰减,衰减由两部分原因组成:一方面是大规模衰减,由路径损耗造成,目标基站定义为$\phi_{n,k}$;另一方面是由信号在空间中多径效应造成的小规模瑞丽衰减,定义为$h_{n,k}$。在异构网络中,用户接收的信号可表示为:
$y=\underbrace{\sqrt{p_n\phi_n}h_nx_n}_{①}+\underbrace{\sum_{u\in N,u\neq n}\sqrt{p_u\phi_u}h_ux_u}_{②}+N_0$

其中第一部分为需要的信号,第二部分为干扰信号,$N_0$为白噪声,其具体大小和该用户所持有的资源块数量成正比,同时包含热噪声谱密度-174 和接收机的噪声系数 $NF = 7$，假设  用户持有 $i$ 个资源块,单个资源块的带宽为 $b$，则白噪声可表示为: $N_{0}\equiv-\;174+10\log_{10}\left(i b\right)+N F$

进一步,对于用户而言其信干噪比 $\gamma$ 可表示为:
$\gamma=\frac{p_{n,k}\phi_{n,k}|h_{n,k}|^{2}}{\sum_{u\in N,u\neq n}p_{u,k}\phi_{u,k}|h_{u,k}|^{2}+N_{0}}$

其中分子部分表示接收信号功率$p_{rx}$,在计算过程中需注意单位匹配，接收信号功率$p_{rx}$单位为mW，而发射功率$p_{n,k}$的单位为dBm，大规模瑞丽衰减$\phi_{n,k}$的单位为dB，小规模衰减$|h_{n,k}|^2$无量纲，因此接收功率可表示为:
$p_{rx}=10^{\frac{p_{n,k}-\phi_{n,k}}{10}}\cdot|h_{n,k}|^2$

根据香农公式,可得用户的传输速率为: $r=ib\cdot\log(1+\gamma)$

## 2. 正交频分多址技术

5G 技术中为了更加灵活的进行资源分配，引入了正交频分多址 (Orthogonal Frequency-Division Multiple Access, OFDMA)。如图 1 所示，在 OFDMA 系统中，频谱资源从频域和时域上进行了划分。在频域上，整个频谱被划分为多个正交的子信道，每个子信道之间的频率存在一定的间隔，以确保它们之间不会互相干扰。在时域上，每个用户在一个时间片内通过子信道进行传输，时间片结束后，若用户任务队列全部完成，则退出子信道，若未完成，则连续占用，若新到达任务，则重新分配。而资源块 (Resource block, RB) 就是在时域和频域上占据一定宽度的连续通信资源的最小单位，每个用户在同一个时间片内可以占用多个相邻的资源块。

为了方便求解,在资源分配过程中,同一用户的多个资源块在频域上相邻,同一切片  的资源块在频域上也相邻。

## 3. 用户服务质量定义

5G 技术中的另一项关键技术是网络切片 (Network Slicing)。它通过网络功能虚拟化和无线资源分配，将单一的物理网络划分成多个逻辑上独立的网络“切片”，每个切片可以为特定的应用场景或用户需求提供定制化的网络服务。网络切片使得同一物理基础设施能够根据不同的服务质量 (QoS) 要求、带宽需求、时延要求等进行灵活配置，从而为不同类型的业务提供量身定制的网络支持。通常，可以将网络按照用户的分类数量分为 3 类切片，分别是：高可靠低时延切片 (Ultra-Reliable and Low-Latency Communications, URLLC)、增强移动宽带切片 (Enhanced Mobile Broadband, eMBB) 和大规模机器通信切片 (Massive Machine Type Communications, mMTC)。而 3GPP 协议为每一类用户定义了具体的服务水平协议 (Service Level Agreement, SLA)，系统在完成不同类型的服务时需满足特定的服务水平协议，在用户任务未在 SLA 规定的延迟时间内完成，则判定任务丢失。三类切片的具体定义如下：

### 第一类是高可靠低时延切片

该切片是为了满足对低时延和高可靠性通信的需求，例如实时控制、医疗和自动驾驶等应用。URLLC 的主要特点是提供极低的时延和高可靠性，以确保实时和关键性应用的可靠性和稳定性。因此对 URLLC 用户任务的服务质量函数为：
$y^{\text{URLLC}} = \begin{cases} \alpha^L & L \le L_{\text{SLA}} \\ -M^{\text{URLLC}} & L > L_{\text{SLA}} \end{cases}$

其中 $\alpha$ 为 URLLC 用户任务效益折损系数，$\alpha\in(0,1)$，$L$ 为用户任务的总延迟，$Q$ 为任务排队延迟，$T$ 为传输延迟，总延迟由排队延迟和传输延迟组成，$L=Q+T$，随着总延迟的增加，用户的服务质量从 1 开始递减至接近 0，在此次赛题中 $\alpha=0.95$。而当用户任务的延迟超过了能忍受的最大值 $L_{SLA}$ 时(见表 1)，判定任务损失，给予 $-M^{\text{URLLC}}$ 的惩罚值。

### 第二类是增强移动宽带

该切片是针对大容量、高速率的宽带数据服务设计的，旨在提供用户更快速、更稳定的无线宽带连接体验。eMBB 的主要特点是提供高速率的数据传输，适用于高清视频、虚拟现实、云游戏和下载等大容量数据的传输。定义用户某时刻的传输速率为 $r$，eMBB 切片的传输速率 SLA 为 $r_{SLA}$，因此对 eMBB 用户任务的服务质量函数为：
$y^{\text{eMBB}} = \begin{cases} 1 & r \ge r_{\text{SLA}}, L \le L_{\text{SLA}} \\ r/r_{\text{SLA}} & r < r_{\text{SLA}}, L \le L_{\text{SLA}} \\ -M^{\text{eMBB}} & L > L_{\text{SLA}} \end{cases}$

其中 $M^{\text{eMBB}}$ 为针对 eMBB 用户任务损失的惩罚值，同样任务损失的条件是超出了时延 SLA，在保证时延的同时需尽可能提升传输速率到达速率 SLA。

### 第三类是大规模机器通信

该切片旨在支持大规模的物联网设备连接，满足海量设备的连接需求，例如智能家居、智能城市和工业自动化等应用。mMTC 的主要特点是大规模设备的连接性、低功耗和低成本。因此在评估对 mMTC 用户任务的服务质量时，侧重连接性，因此在每个决策周期内，假如 mMTC 用户 $i$ 存在任务需要上传，则 $c_i = 1$，若在该周期内成功接入了，则 $c_i^t=1$，否则 $c_i^t=0$，因此，对每个 mMTC 用户任务评估服务质量时，从整体的接入比例角度进行折扣：

$y^{\text{mMTC}} = \begin{cases} \sum_{i\in J}c_i^t / \sum_{i\in J}c_i & L \le L_{\text{SLA}} \\ -M^{\text{mMTC}} & L > L_{\text{SLA}} \end{cases}$

通过以上可知，这三类有各自的服务水平协议，包括最低速率和最大延迟，当多次出现速率不满足时，会降低用户的服务质量，同时超出最大延迟时任务会丢失。三类用户的具体信息如下：

#### 表 1. 网络切片参数
| 类别指标 | URLLC | eMBB | mMTC |
| :--- | :--- | :--- | :--- |
| 每个用户的资源块占用量 | 10 | 5 | 2 |
| SLA:速率 | 10Mbps | 50Mbps | 1Mbps |
| SLA:时延 | 5ms | 100 ms | 500 ms |
| 任务数据量 | 0.01-0.012Mbit | 0.1-0.12Mbit | 0.012-0.014Mbit |
| 任务到达分布 | 泊松分布 | 均匀分布 | 均匀分布 |
| 惩罚系数 M | 5 | 3 | 1 |

## 4. 用户服务流程

基站下行传输用户任务的流程如图 4 所示，系统需要根据用户未来一段时间的情况提前配置不同切片的资源块数量，然后每类用户根据到达任务的先后顺序占用资源块，每一个时间片完成一部分传输，当下时间片任务传输完成就退出资源块，再由其他队列中有等待任务的用户占用资源块并开始传输。由于资源块概率到达，因此每一个时间段需要根据未来任务队列情况重新进行资源分配。

*图 4. 切片划分和任务处理流程图*

在本次赛题中,若存在多个相同类型的用户需要同时接入同一个基站,但基站中该类切片资源不足时,优先处理编号靠前的用户,基于此统一规定用户处理顺序。

## 5. 信号干扰模型

下行传输阶段,通常宏基站和微基站采用不同的频谱进行传输,因此相互之间不会造  成干扰,但是因为频谱资源有限,其中微基站之间会进行频率复用,即不同的基站采用相  同的频率进行传输,此时不可避免地会相互之间造成干扰。

*图 3 展示了干扰形成的原理和模式。*

当两个基站采用相同频率的资源块为两个不同的用户提供服务,不同用户需要的信息会调制到相同的频率,而在用户侧对信息进行解调时,  解调出的信号就会出现混杂,则信息质量就会变差,而合理的功率控制可以有效降低干扰  信号对原始信号的影响。

## 6. 能耗模型

基站的能耗通常包括三个部分,分别为固定能耗,RB 激活能耗以及发射能耗,其中固  定能耗为基站保持基础运行时产生,如散热,控制电路以及时钟同步时,是不可避免的能耗,定义如下:
$P_{\text{static}} = 28 \text{ W}$

RB 激活能耗为每个启用的资源块产生的额外功耗,如基带处理和射频链路激活,其仅  当资源块分配时产生,因此大小正比于资源块分配的数量,具体定义为:
$P_{RB} = \delta \times N_{\text{active}}$

其中 $\delta=0.75\left(W/RB\right)$ 表示激活能耗系数，$N_{active}$ 为资源块启动数量。

最后一项为发射功耗，同时因功率放大器会对信号强度造成一定的损耗，因此最终的功耗定义为：
$P_{tx}=\frac{1}{\eta}p_{transmit}$

其中 $\eta=0.35$ 为损耗系数，$p_{transmit}$ 为发射功耗，由此可得总能耗的计算方式为：
$P=P_{static}+P_{RB}+P_{tx}$

需要注意的是，此处发射功率单位为 W，而在功率决策的是发射功率单位为 dBm，两者转换的公式为：
$p_{(W)}=10^{\frac{p_{(dBm)}-30}{10}}$