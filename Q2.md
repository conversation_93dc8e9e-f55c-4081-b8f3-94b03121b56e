# 问题二解题过程（Q2.md）

## 1. 问题重述
- 时间跨度 1000 ms，每 100 ms 做一次资源划分决策（共 10 次）。
- 任务沿时间按概率到达（由附件 2 的任务流给出），用户位置/信道随时间变化。
- 目标：在 10 次决策下最大化总体用户服务质量（考虑时延 SLA 与未达惩罚）。

## 2. 系统建模
- 时间轴：`t ∈ {0,100,...,900} ms`；每个决策周期细分为 10 个 `Δt=10 ms` 子帧用于仿真。
- 队列：对每个用户维护其任务队列（到达时间、数据量、SLA 截止时间）。
- 决策：每个 `t` 决定 `(x_U(t), x_E(t), x_M(t))`，且 `x_U+x_E+x_M=50`。
- 片内并发能力：`cap_*(t) = ⌊ x_*(t) / 粒度_* ⌋`（URLLC=10，eMBB=5，mMTC=2）。
- 片内调度：每个子帧按“编号靠前优先”选取 `cap_*(t)` 个用户并发服务（若不足则全选）。
- 演化：在 10 个子帧内累计传输数据、更新队列，记完成的任务的完成时间与 `L`，对超时的记惩罚。

## 3. 目标函数
- 最大化 10 个周期内所有任务质量之和：
  - URLLC：`y^{URLLC} = { α^L,  L ≤ L_{SLA};  -M^{URLLC},  L > L_{SLA} }`
  - eMBB：按 `r` 与 `L` 分段（达到速率 SLA 记满分，未达按 `r/r_SLA`，超时 `-M`）
  - mMTC：按接入比例（在每个周期或整体统计时处理，推荐统一在任务完成时计入该周期的比例贡献），超时 `-M`。

## 4. 求解方法一：MPC（推荐）
- 思路：在每个 `t`，向前滚动 `H` 步（建议 3~5）做短期优化，仅执行第一步，提升全局性同时保留实时性。
- 步骤（每个 t）：
  1) 更新队列：加入 `t` 到达任务；
  2) 构建预测窗口 `[t, t+H-1]` 的信道与到达（由数据直接给出或用统计预测）；
  3) 解窗口优化：求 `x_U(τ), x_E(τ), x_M(τ)`（τ∈窗口）最大化预测质量之和；
     - 子问题解法：
       - 小规模：窗口内对每个 τ 穷举/贪心；
       - 中规模：将窗口内的决策写为整数规划（变量数 ≈ `3H`），目标由仿真近似线性化/分段；
  4) 执行 `x_*(t)`，在 10 个子帧内仿真；推进到 `t+100 ms`；
  5) 重复直到 1000 ms。

### 伪代码
```pseudo
for t in {0,100,...,900}:
  enqueue(tasks arriving at t)
  predict channel & arrivals for τ∈[t, t+H-1]
  best = argmax_{x(τ)} PredictedQoS(x(τ), window)
  apply x(t) = best.first_step
  Simulate10Subframes(x(t))
  t = t + 100
```

## 5. 求解方法二：Lyapunov Drift-Plus-Penalty（DPP）
- 定义队列势 `V(Q) = 1/2 Σ Q_i^2`；在每个周期最小化上界：`ΔV + V·(−Reward)` ≈ “压队列 + 争取高质量”。
- 实现：对每个切片计算“单位 RB 的综合权重”（与队列长度/等待时间/接近截止期程度成正比），再按权重贪心分配 50 个 RB。
- 特点：无需预测，低复杂度，能保证队列稳定性与较好长期性能。

## 6. 片内调度与质量记账
- URLLC：更偏向满足时延（优先安排临近 SLA 的任务）；
- eMBB：更偏向提升速率至 `r_SLA`（可在片内优先服务“速率缺口小”的用户以更快达标）；
- mMTC：目标是接入比例（优先服务尚未接入的用户，提升分母已接入比重）。
- 记账：
  - 完成时记录其 `L` 与 `QoS`；
  - 超时任务记 `-M`；
  - mMTC 的“接入比例”建议以每 100 ms 周期为统计窗口：`Σ c_i^t / Σ c_i`。

## 7. 复杂度与工程实现
- MPC：`O(10 × SolveWindow(H))`；H=3~5 时可控。
- DPP：近似 `O(10 × 50 × log(#users))`（每步分配 1 个 RB，动态更新边际收益/权重）。
- 实现建议：与 Q1 复用同一仿真引擎（10 ms 子帧粒度）。

## 8. 输出与分析
- 输出：10 次决策的 `(x_U,x_E,x_M)` 轨迹，周期/整体质量，SLA 违约，平均/95% 时延；
- 对比：MPC vs DPP（或 vs 简单贪心/比例公平），展示优势与开销。

## 9. 备注
- 本问仍未引入跨小区干扰/功率控制；这些将在 Q3 引入；
- 如需进一步提升性能，可加入“任务紧迫度权重”（如 EDF/SRPT 混合）到片内调度。
