# -*- coding: utf-8 -*-
"""
Q1_enumeration.py — 问题一：枚举法求解最优资源分配

基于1ms时间跨度的完整枚举搜索算法：
- 遍历所有可能的资源分配组合 (x_U, x_E, x_M)
- 约束条件：x_U + x_E + x_M = 50，且满足RB粒度要求
- 目标：找到使总QoS最大的最优分配方案

时间参数更新：
- 帧时间：1ms（从10ms更新）
- 资源块时域跨度：1ms
- 资源块频域跨度：360kHz
"""

import os
import json
import math
import random
import time
import csv
import pandas as pd
from dataclasses import dataclass, asdict
from typing import Dict, List, Tuple, Optional
from itertools import product

# 常量（更新时间参数）
RB_TOTAL = 50
RB_GRAN = {"URLLC": 10, "eMBB": 5, "mMTC": 2}
B_HZ = 360_000.0  # 360 kHz
FRAME_MS = 1  # 更新：从10ms改为1ms
FRAME_S = FRAME_MS / 1000.0

# 默认参数
DEFAULT_ALPHA = 0.95
DEFAULT_M = {"URLLC": 5.0, "eMBB": 3.0, "mMTC": 1.0}
DEFAULT_SLA = {
    "URLLC": {"rate_mbps": 10.0, "delay_ms": 5},
    "eMBB": {"rate_mbps": 50.0, "delay_ms": 100},
    "mMTC": {"rate_mbps": 1.0, "delay_ms": 500},
}
DEFAULT_USERS = {"URLLC": 8, "eMBB": 10, "mMTC": 20}
DEFAULT_TASK_BITS_RANGE_MBIT = {
    "URLLC": (0.001, 0.0012),   # 减少到原来的1/10，适应1ms帧
    "eMBB": (0.01, 0.012),      # 减少到原来的1/10，适应1ms帧
    "mMTC": (0.0012, 0.0014),   # 减少到原来的1/10，适应1ms帧
}
DEFAULT_SNR_DB_MEAN_STD = {
    "URLLC": (12.0, 3.0),
    "eMBB": (8.0, 3.0),
    "mMTC": (3.0, 3.0),
}

@dataclass
class User:
    """用户任务"""
    uid: int
    slc: str  # 切片类别
    bits: float  # 任务数据量（bit）
    gamma: float  # 信干噪比 γ（无量纲）
    i_rb: int  # 每用户占用 RB 数（固定，按类别）
    r_bps: float  # 速率（bit/s）
    finish_frame: int | None = None  # 完成所处帧编号（从 1 开始）
    finish_time_s: float | None = None  # 帧内精确完成时刻（秒）

    def L_ms(self) -> float | None:
        return None if self.finish_time_s is None else self.finish_time_s * 1000.0

@dataclass
class AllocationResult:
    """分配结果"""
    allocation: Dict[str, int]  # x_U, x_E, x_M
    capacity: Dict[str, int]    # 并发能力
    total_qos: float
    slice_qos: Dict[str, float]
    slice_stats: Dict[str, Dict]
    simulation_frames: int
    users: Dict[str, List[User]]

class Q1_EnumerationSolver:
    def __init__(self, 
                 n_users: Dict[str, int] = None,
                 alpha: float = DEFAULT_ALPHA,
                 M: Dict[str, float] = None,
                 SLA: Dict = None,
                 task_bits_range: Dict = None,
                 snr_mean_std: Dict = None,
                 seed: int = 42):
        self.n_users = n_users or DEFAULT_USERS.copy()
        self.alpha = alpha
        self.M = M or DEFAULT_M.copy()
        self.SLA = SLA or DEFAULT_SLA.copy()
        self.task_bits_range = task_bits_range or DEFAULT_TASK_BITS_RANGE_MBIT.copy()
        self.snr_mean_std = snr_mean_std or DEFAULT_SNR_DB_MEAN_STD.copy()
        self.seed = seed
        
        random.seed(seed)
        
    def generate_users(self) -> Dict[str, List[User]]:
        """从channel_data.xlsx读取真实用户与任务量（严格按列）"""
        users = {"URLLC": [], "eMBB": [], "mMTC": []}
        uid = 1

        # Excel路径：当前目录下的channel_data.xlsx
        base_dir = os.path.dirname(os.path.abspath(__file__))
        excel_path = os.path.join(base_dir, "channel_data.xlsx")
        if not os.path.exists(excel_path):
            raise FileNotFoundError(f"找不到Excel文件: {excel_path}")

        # 读取Excel文件的第一行数据
        try:
            df = pd.read_excel(excel_path)
            if df.empty:
                raise ValueError("Excel文件为空或无数据行！")

            # 获取第一行数据
            row = df.iloc[0]
        except Exception as e:
            raise ValueError(f"读取Excel文件失败: {e}")

        # 遍历列：U* -> URLLC, e* -> eMBB, m* -> mMTC
        for col_name in df.columns:
            if col_name is None:
                continue
            name = str(col_name).strip()
            if name.lower() == "time" or name == "Time":
                continue

            val = row[col_name]
            if pd.isna(val) or str(val).strip() == "":
                continue
            try:
                mbit = float(val)
            except ValueError:
                continue

            if name.startswith("U") or name.startswith("u"):
                slc = "URLLC"
            elif name.startswith("e"):
                slc = "eMBB"
            elif name.startswith("m"):
                slc = "mMTC"
            else:
                continue

            bits = mbit * 1e6  # Mbit -> bit
            # 生成信道（使用默认SNR分布）
            snr_mean, snr_std = self.snr_mean_std[slc]
            snr_db = random.gauss(snr_mean, snr_std)
            gamma = 10 ** (snr_db / 10.0)
            i_rb = RB_GRAN[slc]
            r_bps = i_rb * B_HZ * math.log(1.0 + gamma)

            users[slc].append(User(
                uid=uid,
                slc=slc,
                bits=bits,
                gamma=gamma,
                i_rb=i_rb,
                r_bps=r_bps
            ))
            uid += 1

        # 打印用户数量以便核对
        print("[Q1_枚举] 从Excel解析人数:", {k: len(v) for k, v in users.items()})
        return users
    
    def generate_all_allocations(self) -> List[Tuple[int, int, int]]:
        """生成所有可能的资源分配组合"""
        allocations = []
        
        # 枚举所有可能的分配
        for x_u in range(0, RB_TOTAL + 1, RB_GRAN["URLLC"]):
            for x_e in range(0, RB_TOTAL - x_u + 1, RB_GRAN["eMBB"]):
                x_m = RB_TOTAL - x_u - x_e
                if x_m >= 0 and x_m % RB_GRAN["mMTC"] == 0:
                    allocations.append((x_u, x_e, x_m))
        
        return allocations
    
    def simulate_allocation(self, allocation: Tuple[int, int, int], users: Dict[str, List[User]]) -> AllocationResult:
        """仿真单个分配方案"""
        x_u, x_e, x_m = allocation
        alloc_dict = {"URLLC": x_u, "eMBB": x_e, "mMTC": x_m}
        
        # 计算并发能力
        capacity = {slc: alloc_dict[slc] // RB_GRAN[slc] for slc in alloc_dict}
        
        # 深拷贝用户数据用于仿真
        sim_users = {}
        for slc in ["URLLC", "eMBB", "mMTC"]:
            sim_users[slc] = []
            for user in users[slc]:
                new_user = User(
                    uid=user.uid,
                    slc=user.slc,
                    bits=user.bits,
                    gamma=user.gamma,
                    i_rb=user.i_rb,
                    r_bps=user.r_bps
                )
                sim_users[slc].append(new_user)
        
        # 仿真传输过程
        frame = 1
        max_frames = 10000  # 防止无限循环
        
        while frame <= max_frames:
            # 检查是否所有任务完成
            all_done = True
            for slc in ["URLLC", "eMBB", "mMTC"]:
                for user in sim_users[slc]:
                    if user.bits > 0:
                        all_done = False
                        break
                if not all_done:
                    break
            
            if all_done:
                break
            
            # 每个切片内并发传输
            for slc in ["URLLC", "eMBB", "mMTC"]:
                cap = capacity[slc]
                if cap <= 0:
                    continue
                
                # 选择前cap个未完成用户
                active_users = [u for u in sim_users[slc] if u.bits > 0][:cap]
                
                for user in active_users:
                    # 计算本帧传输量
                    transmitted_bits = user.r_bps * FRAME_S
                    user.bits = max(0, user.bits - transmitted_bits)
                    
                    # 检查是否完成
                    if user.bits <= 0 and user.finish_time_s is None:
                        user.finish_frame = frame
                        user.finish_time_s = frame * FRAME_S
            
            frame += 1
        
        # 计算QoS
        total_qos, slice_qos, slice_stats = self.calculate_qos(sim_users)
        
        return AllocationResult(
            allocation=alloc_dict,
            capacity=capacity,
            total_qos=total_qos,
            slice_qos=slice_qos,
            slice_stats=slice_stats,
            simulation_frames=frame - 1,
            users=sim_users
        )
    
    def calculate_qos(self, users: Dict[str, List[User]]) -> Tuple[float, Dict[str, float], Dict[str, Dict]]:
        """计算QoS"""
        r_sla_bps = {slc: self.SLA[slc]["rate_mbps"] * 1e6 for slc in self.SLA}
        L_sla_ms = {slc: self.SLA[slc]["delay_ms"] for slc in self.SLA}
        
        total_qos = 0.0
        slice_qos = {"URLLC": 0.0, "eMBB": 0.0, "mMTC": 0.0}
        slice_stats = {}
        
        for slc in ["URLLC", "eMBB", "mMTC"]:
            slice_total = 0.0
            completed = 0
            within_sla = 0
            delays = []

            for user in users[slc]:
                L = user.L_ms()
                if L is not None:
                    completed += 1
                    delays.append(L)
                    is_within = L <= L_sla_ms[slc]
                    if is_within:
                        within_sla += 1

                    if slc == "URLLC":
                        if is_within:
                            y = self.alpha ** L
                        else:
                            y = -float(self.M["URLLC"])
                    elif slc == "eMBB":
                        if is_within:
                            y = 1.0 if (user.r_bps >= r_sla_bps["eMBB"]) else (user.r_bps / r_sla_bps["eMBB"])
                        else:
                            y = -float(self.M["eMBB"])
                    else:  # mMTC - 每个用户贡献1，但总QoS是接入比例
                        if is_within:
                            y = 1.0  # 每个成功接入的用户贡献1
                        else:
                            y = -float(self.M["mMTC"])

                    slice_total += y
                else:
                    # 未完成任务，记为超时
                    y = -float(self.M[slc])
                    slice_total += y

            # mMTC特殊处理：QoS = 接入比例，而不是累加
            if slc == "mMTC":
                mmtc_total = len(users["mMTC"]) if users["mMTC"] else 1
                mmtc_within = within_sla
                slice_total = mmtc_within / mmtc_total if mmtc_total > 0 else 0.0
            
            slice_qos[slc] = slice_total
            total_qos += slice_total
            
            slice_stats[slc] = {
                "total_users": len(users[slc]),
                "completed": completed,
                "within_sla": within_sla,
                "mean_delay": sum(delays) / len(delays) if delays else 0,
                "sla_violation_rate": 1 - within_sla / len(users[slc]) if users[slc] else 0
            }
        
        return total_qos, slice_qos, slice_stats
    
    def solve(self) -> Tuple[AllocationResult, List[AllocationResult]]:
        """枚举求解"""
        print(f"[Q1_枚举] 开始枚举求解，时间跨度更新为{FRAME_MS}ms")
        print(f"[Q1_枚举] 用户配置: {self.n_users}")
        
        # 生成用户
        users = self.generate_users()
        
        # 生成所有可能的分配
        all_allocations = self.generate_all_allocations()
        print(f"[Q1_枚举] 总分配方案数: {len(all_allocations)}")
        
        # 枚举所有分配方案
        results = []
        best_result = None
        best_qos = float('-inf')
        
        start_time = time.time()
        
        for i, allocation in enumerate(all_allocations):
            if (i + 1) % 10 == 0:
                print(f"[Q1_枚举] 进度: {i+1}/{len(all_allocations)} ({(i+1)/len(all_allocations)*100:.1f}%)")
            
            result = self.simulate_allocation(allocation, users)
            results.append(result)
            
            if result.total_qos > best_qos:
                best_qos = result.total_qos
                best_result = result
        
        elapsed_time = time.time() - start_time
        print(f"[Q1_枚举] 枚举完成，用时 {elapsed_time:.2f}s")
        print(f"[Q1_枚举] 最优QoS: {best_qos:.6f}")
        if best_result is not None:
            print(f"[Q1_枚举] 最优分配: {best_result.allocation}")
            return best_result, results
        else:
            raise RuntimeError("未找到可行的最优解，请检查输入数据与约束。")

def main():
    # 创建求解器
    solver = Q1_EnumerationSolver(seed=42)
    
    # 求解
    best_result, all_results = solver.solve()
    
    # 输出结果
    print(f"\n[Q1_枚举] 最优解详情:")
    print(f"分配方案: {best_result.allocation}")
    print(f"并发能力: {best_result.capacity}")
    print(f"总QoS: {best_result.total_qos:.6f}")
    print(f"仿真帧数: {best_result.simulation_frames}")
    
    for slc in ["URLLC", "eMBB", "mMTC"]:
        stats = best_result.slice_stats[slc]
        print(f"{slc}: QoS={best_result.slice_qos[slc]:.6f}, "
              f"完成率={stats['completed']}/{stats['total_users']}, "
              f"SLA达标率={1-stats['sla_violation_rate']:.1%}")
    
    # 保存结果
    output_data = {
        "best_allocation": best_result.allocation,
        "best_qos": best_result.total_qos,
        "best_slice_qos": best_result.slice_qos,
        "best_slice_stats": best_result.slice_stats,
        "simulation_frames": best_result.simulation_frames,
        "frame_time_ms": FRAME_MS,
        "total_allocations_tested": len(all_results),
        "top_10_results": [
            {
                "allocation": r.allocation,
                "qos": r.total_qos,
                "slice_qos": r.slice_qos
            }
            for r in sorted(all_results, key=lambda x: x.total_qos, reverse=True)[:10]
        ]
    }
    
    output_path = "Q1_enumeration_result.json"
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n[Q1_枚举] 结果已保存到: {output_path}")

if __name__ == "__main__":
    main()
