# Q3 讲解

## 1. 问题到底在求什么？
- 多个微基站共用同一频谱（频率复用=1），彼此存在同频干扰。
- 每个微基站每次决策（每 100 ms）要同时决定：
  1) 三类切片的 RB 划分 `x_{b,U}, x_{b,E}, x_{b,M}`（和=50，整数）；
  2) 三个切片统一的发射功率 `p_{b,U}, p_{b,E}, p_{b,M}`（dBm，范围 10–30）。
- 片内：URLLC/eMBB/mMTC 的每用户 RB 粒度分别为 10/5/2；同用户与同切片的 RB 在频域上相邻；调度为 FIFO。
- 目标：在 1000 ms 内最大化所有用户的总服务质量（QoS）。
- 用户接入哪个基站在本问中不做决策（按附件或就近确定）。

## 2. 必备概念（Q3 版本）
- 干扰如何产生：两个不同微基站若在同一“RB 索引”上同时发，互为干扰源（见 Appendix 5）。
- SINR：`γ = S / (I + N0)`，其中 `S` 与 `I` 都要从 dBm/dB 转到线性功率后再计算。
- dBm↔W：`P(W) = 10^{(P(dBm)-30)/10}`。
- OFDMA 相邻性：同一切片的 `x_{b,s}` 个 RB 在频域连续，同一用户的多个 RB 也连续（Appendix 2）。

## 3. 我们在“分什么 + 调什么”
- “分”：每个基站 b，把 50 个 RB 在三类切片间划成三个连续区段（相邻性）。
- “调”：给每个切片一个统一的功率（10–30 dBm）。
- 两者强耦合：功率改变了干扰格局→影响速率→影响“该切片多要/少要 RB”的收益。

## 4. 为什么需要“交替优化”？
- 直接把“RB 划分 + 功率”一次性最优化会很难（离散 + 非凸）。
- 交替优化：固定 RB → 调功率；固定功率 → 调 RB；循环 2~5 次，多数情况下就能明显提升。
- 外层再配上 Q2 用过的“滚动优化（MPC）或 DPP 在线法”，兼顾长期性与实时性。

## 5. 两条主线方法
- 方法 A：交替优化（推荐）
  - 固定 RB，按切片权重（队列紧迫度、SLA 接近程度）做功率爬山/投影；
  - 固定功率，枚举有限的“相邻切分候选”（步长取 10/5/2）选最优；
  - 在每个 100 ms 决策时刻迭代 3~5 轮。
- 方法 B：DPP + 分布式功率控制
  - DPP 产生切片权重，50 个 RB 逐个贪心分配给“单位增益最大的 (b,s)”；
  - 每步或每几步做一次小幅度功率更新（分布式/并行），降低干扰、提升 SINR。

## 6. 计算细节与易错点
- 单位：功率、噪声必须转线性再入 `log(1+γ)`；`NF`、`-174 dBm/Hz` 记得换算到 i·b 带宽。
- 干扰对齐：需定义一个“跨基站的 RB 索引对齐”，同一索引才会互相干扰。
- 相邻性：同切片/同用户 RB 连续；违反就不合法。
- 上下界：每切片功率裁剪在 [10,30] dBm。
- 片内并发：`cap_{b,U}=⌊x_{b,U}/10⌋` 等，请用 10 ms 子帧推进排队与传输。

## 7. 输出与可视化
- 输出：每周期各基站 `(x_{b,U},x_{b,E},x_{b,M})` 与 `(p_{b,U},p_{b,E},p_{b,M})`，总 QoS、SLA 违约数、时延统计。
- 图形：
  - 干扰热图（基站对之间的平均干扰）；
  - 交替优化的收敛曲线（轮次 vs QoS）；
  - 功率分布随时间的变化；
  - 不同方法（交替 vs DPP）对比。

## 8. 写作建议（报告）
- 先给“系统图 + 干扰示意图”；
- 再给模型（变量、约束、目标）与算法伪代码；
- 展示对比图（QoS、违约、时延）与灵敏度分析（步长、候选数量、迭代轮次）。
