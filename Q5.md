# 问题五解题过程（Q5.md）

## 1. 问题重述
- 场景：在问题四（宏-微异构：1 个宏基站 + 多个微基站）的基础上，进一步引入能耗优化目标。
- 频谱与干扰：宏与微使用不同频谱，宏↔微无干扰；微↔微同频复用产生干扰（`Appendix.md` §5）。
- 资源与粒度：宏 100 RB，微 50 RB；URLLC/eMBB/mMTC 的 per-user RB 粒度分别为 10/5/2；同切片与同用户的 RB 在频域相邻（`Appendix.md` §2）。
- 功率范围：宏每切片 10–40 dBm；微每切片 10–30 dBm。
- 决策周期：每 100 ms 一次（总 1000 ms，10 次）；周期内以 10 个 10 ms 子帧仿真 FIFO 调度。
- 需联合决策（每周期）：
  1) 用户接入：宏 vs 最近微（唯一二选一）；
  2) 各基站三类切片的 RB 连续划分（相邻性）；
  3) 各基站三类切片统一的发射功率；
  4) 能耗相关的“激活 RB 数/占空比/睡眠（若题面允许）”。
- 目标：在保证 QoS/SLA 的同时优化能耗，可选目标包括最小能耗、质量-能耗加权和或能效最大化（`Appendix.md` §6）。

## 2. 系统与信道模型（与 Q4 一致）
- SINR 与速率：
  - 宏服务用户 k：`γ = S/N0`，`S = 10^{(p_{M,s}-φ_{M,k})/10}|h_{M,k}|^2`（mW）；
  - 微 b 服务用户 k：`γ = S/(I+N0)`，`I = Σ_{u≠b} 10^{(p_{u,s_u}-φ_{u,k})/10}|h_{u,k}|^2`（同 RB 索引上）；
  - 速率：`r = i·b·log(1+γ)`，`i∈{10,5,2}`，`b=360 kHz`；功率/噪声均需 dBm→线性。
- 片内并发：`cap_{*,U}=⌊x_{*,U}/10⌋`，`cap_{*,E}=⌊x_{*,E}/5⌋`，`cap_{*,M}=⌊x_{*,M}/2⌋`；10 ms 子帧 FIFO。

## 3. 能耗模型（严格对齐 Appendix §6）
- 常量与换算：`P_static = 28 W`，`δ = 0.75 (W/RB)`，`η = 0.35`；`P(W)=10^{(p(dBm)-30)/10}`。
- 站点 t 时刻的功耗：
  - `P_b(t) = P_static + δ·N_active_b(t) + P_tx_b(t)/η`。
  - `N_active_b(t)`: 子帧内实际在发的 RB 总数（≤ 分配上限；当队列不足或限功率/限占空比时可小于上限）。
  - `P_tx_b(t)`: 站点在子帧内的发射功率（W）。在“每切片统一 dBm”设定下，可取：
    - `P_tx_b(t) = Σ_{s∈{U,E,M}} N_active_{b,s}(t) · P_lin(b,s)`，其中 `P_lin(b,s)=10^{(p_{b,s}(dBm)-30)/10}`。
- 周期/全程能量：`E_total = Σ_{t, subframe} Σ_b P_b(t) · Δt`（`Δt=10 ms`）。

## 4. 决策变量与约束（每周期 t）
- 变量：
  - 接入：`a_k^M∈{0,1}`、`a_k^S∈{0,1}`，且 `a_k^M+a_k^S=1`；
  - RB：宏 `x_{M,s}∈ℕ`，`Σ_s x_{M,s}=100`；微 `x_{b,s}∈ℕ`，`Σ_s x_{b,s}=50`；三段连续、不重叠；
  - 功率：宏 `p_{M,s}∈[10,40] dBm`，微 `p_{b,s}∈[10,30] dBm`；
  - 占用：`N_active_{b,s}(t) ≤ x_{b,s}`（子帧内实际启用 RB 数）；
  - 可选睡眠：`state_b(t)∈{ACTIVE,SLEEP}`（若题面允许），SLEEP 时 `P_b≈P_sleep` 且需计切换代价 `C_sw`。
- 约束：
  - QoS/SLA：按 `Appendix.md` §3 定义（URLLC 时延优先；eMBB 分段；mMTC 接入比例与超时惩罚）。
  - 干扰与相邻性：`Appendix.md` §5 与 §2。
  - 片内：FIFO 与并发由 `cap` 决定；`N_active` 与实际并发一致。

## 5. 目标函数（三种常见形式）
- 形式 A（最小能耗，满足 QoS）：
  - `min E_total` s.t. QoS/SLA 与资源/功率/接入/相邻性约束。
- 形式 B（质量-能耗加权和）：
  - `max Σ QoS − λ·E_total`（扫描 `λ` 得 Pareto 折中）。
- 形式 C（能效最大化）：
  - `max (Σ QoS) / E_total`（可用 Dinkelbach 分式规划）。

## 6. 求解方法一：MPC + 分层交替 + 能耗感知（推荐）
- 外层：MPC/滚动窗口（H=3~5 个 100 ms），仅执行第一步在线决策。
- 内层（单周期 t，交替迭代 2~5 轮）：
  1) 子问题 A：接入 + RB 连续划分（固定功率）。以“单位 RB 质量增益 − λ·能耗代价”为效用，枚举有限连续切分（步长 10/5/2）。
  2) 子问题 B：功率控制（固定接入与 RB）。对 `p_{*,s}` 做投影梯度/WMMSE/标准干扰函数更新，裁剪至功率范围；
  3) 子问题 C：占空比/激活 RB 优化。给定 RB 与功率，限制 `N_active_{b,s}(t)` 使“边际质量 < 边际能耗”时关断部分 RB；
  4) 执行方案推进 10×10 ms 子帧，计 QoS 与能量；滚动到下一周期。

### 伪代码（λ-加权为例）
```pseudo
input: queues, channels, prev (A,X,P), λ
warm-start (A,X,P)
for iter=1..K:
  (A,X) ← SolveAccessAndRB_Adjacency(P, λ)   # 单位增益-λ·能耗
  P     ← SolvePower(A,X)                    # 投影梯度/WMMSE
  Nact  ← ThrottleActiveRB(A,X,P, λ)         # 占空比/启用RB数
execute (A,X,P,Nact); Accumulate QoS & Energy
```

## 7. 求解方法二：Dinkelbach（能效最大化）+ 工程启发式
- 令 `f(P)=Σ QoS`，`g(P)=E_total`，迭代 `q_{m+1} = f(P^*)/g(P^*)`，每步解 `max f(P) − q_m g(P)`（与形式 B 等价），内部仍用“接入+RB”和“功率”交替 + 占空比限制。
- 工程近似：
  - 先固定接入为“最近微/宏偏置”，用有限候选切分 + 少量功率步进；
  - 周期内开关少量 RB（或限并发）形成“脉冲式发送”，以换取休眠窗口；
  - 仅在边缘用户做“宏↔微”切换尝试，若`Δ(QoS−λ·E)`>0 则接受。

## 8. 片内调度与记账
- 每子帧：按 FIFO 不超过 `cap` 服务；统计每站 `N_active_b(t)` 与各切片 `N_active_{b,s}(t)`；
- 质量：按三类 SLA 计 `QoS`；
- 能量：按 `P_b(t)` 汇总到 `E_total`（`Δt=10 ms`）。

## 9. 复杂度与实现建议
- 复杂度：`O(K × (接入+切分枚举 + 功率更新 + RB 启停))`；`K≈3~5`。
- 建议：
  - 继承 Q4 的仿真与数据结构；
  - “有限候选连续切分”控制组合规模；
  - RB 启停/限并发优先在负载低时生效，避免 SLA 风险；
  - 若题面要求 `Σ x=上限`，仍可通过“少启用 RB（N_active<x）/短时高并发后休眠”获取能耗收益。

## 10. 输出与分析
- 输出：每周期的接入、各站 `(x_{*,U},x_{*,E},x_{*,M})`、`(p_{*,U},p_{*,E},p_{*,M})`、`N_active`、总 `E_total`、总 `QoS`、SLA 违约、平均/95% 时延。
- 可视化：
  - 质量-能耗 Pareto 前沿（扫描 `λ` 或 Dinkelbach 历程）；
  - 功率/启用 RB/占空比随时间曲线；
  - 与基线（仅 QoS 最大化、固定功率）对比。

## 11. 备注
- 单位统一：dBm↔W、J= W·s；`Δt=10 ms`；
- 参考：`Appendix.md` §2（相邻性）、§3（质量）、§5（干扰）、§6（能耗）。
