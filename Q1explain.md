# Q1 讲解

## 1. 问题到底在求什么？
- 场景：一个微基站、固定总资源 50 个 RB（Resource Block，资源块）。
- 任务：把 50 个 RB 按三类切片（URLLC/eMBB/mMTC）划分，片内再按“编号靠前优先”的顺序服务用户。
- 目标：让“所有用户的服务质量（QoS）之和”最大。

## 2. 入门概念（超简版）
- RB（资源块）：时频上的最小分配单位，题目设定“一个用户一次占用的 RB 数量是固定粒度”。表 1 给出：URLLC=10，eMBB=5，mMTC=2。
- OFDMA：把频谱切成很多子带（RB），不同用户同时占不同的 RB 互不干扰。
- dB / dBm / W：
  - dBm 是功率的对数单位（相对 1 mW），换算：`P(W) = 10^{(P(dBm)-30)/10}`。
  - dB 是“增益/损耗”的对数单位（无量纲）。
- SINR：信号功率 / (干扰功率 + 噪声功率)。本问一般不强调小区间干扰，可近似 I≈0。
- 速率公式：`r = i·b·log(1+SINR)`，i 是该类用户固定占用的 RB 数；b=360 kHz。
- 时延 L：= 排队 Q + 传输 T。
- QoS：三类切片各自有定义（详见 Appendix）：
  - URLLC：低时延最重要（延迟超过 SLA 直接惩罚）。
  - eMBB：高数据率重要（达不到速率 SLA 就按比例折扣）。
  - mMTC：更看接入比例（是否“被服务”）。

## 3. 我们在“分什么”？
- 决策变量：`x_U, x_E, x_M` 分别是三类切片拿到的 RB 数，且 `x_U + x_E + x_M = 50`。
- 并发能力（本质上是“能同时服务几个用户”）：
  - `cap_U = ⌊x_U/10⌋`, `cap_E = ⌊x_E/5⌋`, `cap_M = ⌊x_M/2⌋`。
- 片内调度：每个 10 ms 子帧里，每类最多挑 `cap_*` 个队首用户同时服务。

## 4. 怎么算“一个方案”的好坏？
- 给定 `(x_U,x_E,x_M)`：
  1) 算 `cap_*`；
  2) 在若干个 10 ms 子帧内做“片内仿真”：
     - 每个子帧：各类从队首选不超过 `cap_*` 个用户并发；
     - 被选用户：按 `r = i·b·log(1+SINR)` 传数据，直到完成；
     - 记录每个任务的完成时间 L=Q+T，随后按其切片 QoS 公式打分；
  3) 全部任务求和，得到该分配方案的总 QoS。

## 5. 两种求解方法
- 方法 A：穷举（推荐，规模可控）
  - 对所有 `x_U=0..50, x_E=0..50-x_U`（`x_M=50-x_U-x_E`）都试一遍，挑 QoS 最大的。
  - 优点：简单、结果靠谱；缺点：比贪心慢一点，但 N=50 仍可接受。
- 方法 B：边际收益贪心（近似）
  - 从 `(0,0,0)` 开始，每次把 1 个 RB 放给“当前带来 QoS 增益最大的切片”。
  - 直觉：当 `x_*` 跨过该类粒度（如 URLLC 的 10）时，并发能力+1，往往带来明显增益。

## 6. 计算细节与易错点
- 单位要统一：噪声公式给的是 dBm，参与 SINR 前需转线性功率（mW/W）。
- log 的底：按 Appendix 不改写，统一使用其 `log(1+·)` 定义。
- 编号靠前优先：队列顺序会影响排队时间 Q，从而影响 L 与 QoS。
- eMBB 的 QoS 分段条件中，逗号分隔条件（`r ≥ r_SLA, L ≤ L_SLA`）保证 LaTeX 正确渲染。

## 7. 一个小小的“拍脑袋”直觉
- URLLC 更吃并发能力（降时延），所以给够“10 的整数倍”很关键；
- eMBB 在接近 `r_SLA` 时的“最后一点 RB”很值钱；
- mMTC 追求“接入覆盖率”，给少量 RB 也可能迅速提升比例。

## 8. 输出与如何写报告
- 报告结构：问题重述→建模（变量/约束/目标）→算法与伪代码→结果与可视化（热图/表格）→灵敏度分析（±RB 调整）。
- 可视化建议：
  - 最优 `(x_U,x_E,x_M)` 方案；
  - QoS 随 RB 划分变化的热图；
  - 各类用户的完成时延分布（均值/95分位）。

## 9. 进一步提升（选读）
- 若考虑功率控制或干扰（在后续问题），可把“每类 RB 划分”与“功率”交替优化。
- 也可把片内调度从 FIFO 换成 EDF（最早截止期优先）等做对照实验。
