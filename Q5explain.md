# Q5 讲解（能耗优化）

## 1. 问题到底在求什么？
- 在 Q4 的异构网络（1 宏 + 多微）基础上，除了保证三类切片的服务质量（QoS/SLA），还要“优化能耗”。
- 每 100 ms 做一次联合决策（共 10 次）：
  1) 用户接入：宏 vs 最近微（二选一）。
  2) 各基站三类切片的 RB 划分（连续相邻）。
  3) 各基站三类切片的统一发射功率（宏 10–40 dBm，微 10–30 dBm）。
  4) 子帧内“实际启用的 RB 数 N_active”/占空比（以及可选的站点睡眠）。
- 目标可有三种典型形式：
  - 最小化总能耗 E_total，满足 QoS/SLA。
  - 最大化 ΣQoS − λ·E_total（λ 扫描得折中曲线）。
  - 最大化能效 (ΣQoS)/E_total（用 Dinkelbach）。

## 2. 必备概念（能耗模型与单位）
- 按 `Appendix.md` §6：
  - 固定能耗：P_static = 28 W。
  - RB 激活能耗：P_RB = δ·N_active，δ = 0.75 W/RB。
  - 发射能耗：P_tx = p_transmit/η，η = 0.35。
  - 站点功耗：P_b(t) = P_static + δ·N_active_b(t) + P_tx_b(t)/η。
  - 总能量：E_total = Σ_{t,subframe} Σ_b P_b(t)·Δt，Δt=10 ms。
- dBm↔W：P(W)=10^{(P(dBm)−30)/10}。噪声/功率进入 SINR/速率计算时需用线性单位。
- OFDMA 相邻性（`Appendix.md` §2）：同切片/同用户 RB 连续。
- 干扰（`Appendix.md` §5）：微↔微在“相同 RB 索引”上互扰；宏↔微频谱不同，无互扰。

## 3. 我们在“连什么 + 分什么 + 调什么 + 省什么”
- 连（接入）：宏或最近微。
- 分（RB 切片）：每个站将（100 或 50）RB 划为 U/E/M 三个连续段。
- 调（功率）：每个站、每切片一个统一 dBm 功率。
- 省（占空比/睡眠）：子帧内只启用必要的 RB（N_active ≤ 分配上限），负载低时减少启用数；若允许睡眠，可在空窗进入 SLEEP（考虑切换代价）。

## 4. 为什么是多目标？如何折中？
- QoS 与能耗冲突：更多 RB/更高功率有利于 QoS 但耗电更高。
- 三种目标的等价性：
  - ΣQoS − λ·E_total（加权和）与 Dinkelbach的“f − qg”在每步等价。
  - 实践中常扫描 λ 得到 Pareto 前沿，选一个折中点。

## 5. 两条主线方法
### 5.1 MPC + 分层交替 + 能耗感知（推荐）
- 外层：MPC（滚动窗口 H=3~5），只执行第一步。
- 内层（单周期 t，迭代 2~5 轮）：
  1) 接入 + RB（固定功率）：按“单位 RB 质量增益 − λ·能耗代价”挑选连续切分（步长用 10/5/2 枚举）。
  2) 功率（固定接入/RB）：投影梯度/WMMSE/标准干扰函数更新，裁剪到范围。
  3) 占空比：在满足 SLA 的前提下，减少 N_active（或限并发）以降低 δ·N_active 与发射能耗。
- 片内：10 ms 子帧 FIFO，统计 QoS 与 P_b(t)。

### 5.2 Dinkelbach（能效最大化）+ 工程启发式
- 迭代 q：每步解 max ΣQoS − q·E_total。内部仍用“接入+RB”与“功率”交替，再附带占空比控制。
- 工程近似：
  - 先用“最近微/宏偏置”给初始接入；
  - RB 只枚举有限连续切分；
  - 功率做小步长投影更新；
  - 低负载子帧关停部分 RB 形成“脉冲式发送”，换取休眠窗口。

## 6. 计算细节与易错点
- 单位：dBm↔W，J = W·s；Σ能量按子帧聚合（Δt=10 ms）。
- 干扰对齐：微↔微必须对齐 RB 索引；宏与微不互扰。
- 相邻性：切片三段连续且互不重叠；单用户所占 RB 连续。
- SLA：等号计为满足侧；mMTC 用接入比例定义 QoS（见 `Appendix.md` §3）。
- 如果题面强制“Σx = 上限”（例如宏 100、微 50 必须分满），也可以通过“实际启用 N_active < x”与“提前完成后休眠”来节能。

## 7. 可落地实施路线（建议）
1) 复用 Q4 的仿真内核（SINR/速率、FIFO、SLA、干扰索引对齐、相邻切分枚举）。
2) 实装能耗计量：P_static、δ·N_active、p(dBm)→W→P_tx/η，按子帧累加 E_total。
3) 增加 N_active 控制与（可选）睡眠逻辑，保证与队列/并发一致。
4) 实现 λ-加权的内层交替；随后加入 Dinkelbach 以做能效目标。
5) 画 Pareto 曲线（λ 扫描），比对 QoS、能耗、能效，给出推荐点。

## 8. 输出与可视化
- 输出：每周期接入、(x_{*,U},x_{*,E},x_{*,M})、(p_{*,U},p_{*,E},p_{*,M})、N_active、E_total、ΣQoS、SLA 违约、时延统计。
- 图形：
  - 质量-能耗 Pareto 前沿；
  - 功率/启用 RB/占空比随时间；
  - 与仅 QoS 最大化的基线对比。

## 9. 写作建议（报告）
- 先画系统/干扰/能耗组成示意图；
- 给出能耗模型、目标函数、变量与约束；
- 附上交替/MPC/Dinkelbach 的流程图/伪代码；
- 展示 Pareto 与灵敏度分析（λ、窗口 H、功率步长、候选切分数、N_active 门限）。
