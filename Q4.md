# 问题四解题过程（Q4.md）

## 1. 问题重述
- 场景：一座宏基站（MBS）+ 多个微基站（SBS）。宏/微使用不同频谱，因此“宏↔微无干扰”，仅“微↔微同频干扰”。
- 资源：宏基站 100 个 RB；每个微基站 50 个 RB。三类切片用户的 per-user RB 粒度：URLLC=10，eMBB=5，mMTC=2（见 `Appendix.md` 表 1）。
- 功率范围：宏基站每切片功率 `10–40 dBm`；微基站每切片功率 `10–30 dBm`（见 `Topic.md`）。
- 决策周期：每 100 ms 一次（总 1000 ms，10 次）；周期内用 10 个 10 ms 子帧推进仿真。
- 需同时决定（每周期）：
  1) 用户接入：每个用户在“最近微基站”与“宏基站”二者中二选一；
  2) 各基站三类切片的 RB 划分（相邻性约束）；
  3) 各基站三类切片的统一发射功率。
- 目标：最大化全体用户在 1000 ms 内的总体服务质量（QoS），SLA 违约按 `Appendix.md` 计惩罚。

## 2. 系统建模
- 基站：宏 `b=M`，微 `b∈B`。宏 100 RB、微 50 RB。
- 时间：`t ∈ {0,100,...,900} ms`；每周期含 10 个 `Δ=10 ms` 子帧。
- OFDMA 相邻性：同切片的 RB 在频域连续；同一用户占用的多个 RB 也需相邻（`Appendix.md` 第 2 节）。
- 干扰：
  - 宏：与微频谱不重叠，无跨层干扰；宏内只考虑噪声（及同站内用户正交后互不干扰）。
  - 微：不同微站在相同 RB 索引上会产生同频干扰（`Appendix.md` 第 5 节）；需对齐跨站 RB 索引。
- SINR 与速率：
  - 宏服务用户 k：`γ = S/(N0)`，`S = 10^{(p_{M,s} - φ_{M,k})/10}·|h_{M,k}|^2`（mW）；
  - 微 b 服务用户 k：`γ = S/(I+N0)`，`I = Σ_{u∈B,u≠b} 10^{(p_{u,s_u}-φ_{u,k})/10}·|h_{u,k}|^2`（同 RB 索引上）；
  - `r = i·b·log(1+γ)`，其中 `i∈{10,5,2}`，`b=360 kHz`，功率/噪声需做 dBm→线性换算（`Appendix.md`）。
- 片内并发能力：
  - 宏：`cap_{M,U}=⌊x_{M,U}/10⌋`，`cap_{M,E}=⌊x_{M,E}/5⌋`，`cap_{M,M}=⌊x_{M,M}/2⌋`；
  - 微 b：`cap_{b,U}=⌊x_{b,U}/10⌋`，`cap_{b,E}=⌊x_{b,E}/5⌋`，`cap_{b,M}=⌊x_{b,M}/2⌋`。
- 片内调度：每 10 ms 子帧，FIFO 选不超过 `cap_*` 的队首用户并发服务。

## 3. 决策变量与约束（每周期 t）
- 变量：
  - 接入二元：`a_k^M(t) ∈ {0,1}`（宏），`a_k^S(t) ∈ {0,1}`（最近微）；且 `a_k^M + a_k^S = 1`；
  - RB：宏 `x_{M,s}(t)`，微 `x_{b,s}(t)`；
  - 功率：宏 `p_{M,s}(t) ∈ [10,40] dBm`，微 `p_{b,s}(t) ∈ [10,30] dBm`。
- 约束：
  - 资源：`Σ_s x_{M,s}=100`，`∀b: Σ_s x_{b,s}=50`；`x` 为非负整数；
  - 相邻性：各基站的三个切片区段在频域上为三个不重叠的连续段；
  - 并发/粒度：按切片粒度确定 `cap_*`；
  - 关联服务：若 `a_k^M=1` 则仅由宏在其切片上服务；若 `a_k^S=1` 则仅由最近微基站服务；
  - 时延与 QoS 计算：按 `Appendix.md` 第 3 节定义与 SLA 规则。
- 目标：最大化 `Σ_{t} Σ_{k} QoS_k(t)`。

## 4. 求解方法一：分层交替优化（推荐）
- 外层（时间滚动）：沿用 Q2 的 MPC 或 DPP，在每个决策时刻仅执行第一步/一步在线决策；
- 内层（单周期 t）：分两层交替，先做“用户接入 + RB 划分”，再做“功率控制”，迭代 2~5 轮。

### 4.1 子问题 A：用户接入 + RB 划分
- 目标：给定功率 `P`，最大化 `Σ_b U_b(X_b, A | P)`；
- 约束：`Σ_s x_{M,s}=100`，`Σ_s x_{b,s}=50`，相邻性，整数；`A` 满足每用户二选一；
- 方法：
  - 候选接入：对每个用户，只考虑“最近微 or 宏”两个候选；
  - 快速评估：用当前功率 + 信道估计两侧的“单位资源效用”，先给出初始接入；
  - RB 连续切分：宏与每个微站把（100 或 50）RB 划成 3 段，步长取 10/5/2 粒度枚举；
  - 交替微调：在接入与切分上做少量贪心/交换提升（如把边缘用户切给宏以缓解干扰）。

### 4.2 子问题 B：功率控制
- 目标：固定 `A,X`，最大化各基站切片加权效用；宏功率约束 [10,40]，微 [10,30]；
- 方法：
  - 梯度投影/爬山，或 WMMSE/标准干扰函数法；
  - 宏与微分层更新：先更新微（缓解互扰），再更新宏（提升全局覆盖用户的速率）。

### 4.3 伪代码（单周期 t）
```pseudo
input: queues, channels, prev A,X,P
A,X,P ← warm-start
for iter=1..K (K=3~5):
  A,X ← SolveAccessAndRB(P)
  P   ← SolvePower(A,X)
execute (A,X,P) at t; Simulate10Subframes(A,X,P); t←t+100
```

## 5. 求解方法二：联合贪心 + 层内微调（工程友好）
- 先按“单位资源权重”把用户粗分到宏或最近微；
- 再对每个基站用相邻性枚举做 3 段 RB 切分；
- 周期内穿插少量功率步进更新；
- 对少量边缘用户做“换站”试探，若 QoS 提升则接受。

## 6. 片内调度与质量记账
- 宏/微统一：每 10 ms 子帧，FIFO 调度不超过 `cap_*` 的用户；完成记 `L=Q+T` 与 QoS；超时记 `-M`；mMTC 接入比例按周期统计。

## 7. 复杂度与实现建议
- 分层交替：`O(K × (接入+切分开销 + 功率迭代开销))`；
- 建议：
  - 复用 Q2/Q3 仿真引擎；
  - 采用“粒度步长的有限候选”以控制 RB 枚举规模；
  - 接入只在宏 vs 最近微之间切换，避免组合爆炸。

## 8. 输出与分析
- 输出：每周期的接入决策、各基站 `(x_{*,U},x_{*,E},x_{*,M})`、`(p_{*,U},p_{*,E},p_{*,M})`，总 QoS、SLA 违约、时延统计。
- 可视化：
  - 接入热力图（宏/微覆盖与分担）；
  - QoS 与功率随时间曲线；
  - 与基线（仅最近微接入、固定功率）对比。

## 9. 备注
- 单位换算与 SLA 规则严格按 `Appendix.md`；
- 功率范围：宏 10–40 dBm、微 10–30 dBm；
- 相邻性：同切片与同用户 RB 在频域连续；
- 三文档联动：本问是 Q3 的超集（多了宏与接入决策），Q5 在本问基础上加入能耗目标。
