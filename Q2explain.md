# Q2 讲解

## 1. 问题到底在求什么？
- 时间跨度 1000 ms，每 100 ms 决策一次（共 10 次）。
- 每次决策要把 50 个 RB 在三类切片（URLLC/eMBB/mMTC）之间重新划分；片内再按“编号靠前优先”服务队列中的任务。
- 用户任务沿时间随机到达、用户位置与信道也随时间变化。
- 目标：10 次决策下，使“整体服务质量（QoS）之和”最大。

## 2. 入门概念（复习 + 动态视角）
- 队列（Queue）：任务来了先排队，等分配到资源才被服务；完成时间 L = 排队时间 Q + 传输时间 T。
- 时间轴：t=0,100,...,900 ms 是“决策周期”；每个周期内部可细分成 10 个 10 ms 的“子帧”做传输仿真。
- 并发能力：由切片拿到的 RB 数决定（URLLC 每用户需 10 RB；eMBB 每用户 5 RB；mMTC 每用户 2 RB）。
- SINR/速率/质量函数：与 Q1 相同，但要“随时间”不断重新计算。

## 3. 为什么不能“一次定到头”？
- 动态耦合：当前的分配会影响今后的队列长度/等待时间，以及是否超时，进而影响今后的最优决策。
- 因此更适合“滚动优化”或“在线决策”，每到一个决策时刻，基于当前状态做一次“前瞻 + 决策”。

## 4. 两条主线方法
### 4.1 MPC（Model Predictive Control，滚动优化，推荐）
- 思路：在 t 时刻，向前看 H 个决策（如 H=3~5），对这 H 步做一个短期优化，得到一条未来分配序列；但实际只执行“第一步”，然后时间推进到 t+100 ms 再滚动。
- 好处：兼顾当前与未来；坏处：需要一点点“预测”（或使用附件中直接给的未来信息）。
- 步骤：
  1) 加入本周期新到任务，形成当前队列；
  2) 取窗口 τ∈[t, t+H-1] 的信道/到达（由数据或统计预测）；
  3) 在窗口内最大化预测 QoS 之和，变量是 x_U(τ),x_E(τ),x_M(τ)（且三者和=50）；
  4) 只执行 τ=t 的结果，在 10 个子帧内做片内仿真，更新队列，进入下一个周期。

### 4.2 DPP（Lyapunov Drift-Plus-Penalty，稳定性优先的在线分配）
- 定义一个“势能”V(Q)=1/2·ΣQ_i^2，目标是在每步既减少排队（Drift 小）又争取高 QoS（Penalty 小）。
- 做法：为每个切片计算“单位 RB 的权重”，权重和队列长度/等待时间/接近截止期程度正相关，然后把 50 个 RB 逐个贪心分给权重更大的切片。
- 优点：不需要预测、低复杂度；缺点：对未来的把控较弱。

## 5. 片内调度与小技巧
- 题面要求默认“编号靠前优先（FIFO）”，请严格遵守（可把其他调度作为对照试验）。
- 小技巧：
  - URLLC：优先赶在 L_SLA 之前完成（强烈受益于增加并发能力）。
  - eMBB：让更多用户速率达到 r_SLA（接近阈值的用户“最后一点资源”很值钱）。
  - mMTC：提高接入比例（更多用户被成功服务）。

## 6. 评价指标与可视化
- 指标：总 QoS、各类 QoS、SLA 违约数、平均/95% 时延、吞吐量。
- 图形：
  - 10 个周期的 (x_U,x_E,x_M) 轨迹（堆叠柱状图）；
  - QoS 随时间曲线；
  - 时延分布（CDF/箱线图）；
  - 队列长度随时间变化。

## 7. 易错点
- 单位：dBm↔W、kHz↔Hz、SINR 必须用线性功率计算后再带入 log(1+·)。
- SLA 边界：等号属于“满足”一侧（按 Appendix 约定）。
- 预测与现实偏差：MPC 用到的窗口 H 不宜太长（过长会“想太多”），一般 3~5 步较稳。
- 子帧仿真：别忘了在每个 100 ms 决策内用 10 个 10 ms 子帧推进传输与排队。

## 8. 简单可行的起步路线（建议）
1) 先做一个“贪心”或“固定比例”基线，保证流程跑通；
2) 实现 MPC 的窗口优化：窗口内逐步穷举/贪心（规模可控）；
3) 记录并对比指标：MPC vs 基线；
4) 做灵敏度分析：窗口长度 H、随机种子、任务强度变化等。

## 9. 输出怎么写在论文里
- 先解释动态决策的必要性（队列与 SLA 的时间耦合）。
- 给出模型：状态、决策、演化、目标；
- 给出算法流程图/伪代码；
- 展示曲线和表格，解释“为什么方案更好”。
