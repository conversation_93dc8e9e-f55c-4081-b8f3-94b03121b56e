{"best_allocation": {"URLLC": 0, "eMBB": 0, "mMTC": 50}, "best_qos": -22.0, "best_slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}, "best_slice_stats": {"URLLC": {"total_users": 2, "completed": 0, "within_sla": 0, "mean_delay": 0, "sla_violation_rate": 1.0}, "eMBB": {"total_users": 4, "completed": 0, "within_sla": 0, "mean_delay": 0, "sla_violation_rate": 1.0}, "mMTC": {"total_users": 10, "completed": 0, "within_sla": 0, "mean_delay": 0, "sla_violation_rate": 1.0}}, "simulation_frames": 10000, "frame_time_ms": 1, "total_allocations_tested": 21, "top_10_results": [{"allocation": {"URLLC": 0, "eMBB": 0, "mMTC": 50}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 0, "eMBB": 10, "mMTC": 40}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 0, "eMBB": 20, "mMTC": 30}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 0, "eMBB": 30, "mMTC": 20}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 0, "eMBB": 40, "mMTC": 10}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 0, "eMBB": 50, "mMTC": 0}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 10, "eMBB": 0, "mMTC": 40}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 10, "eMBB": 10, "mMTC": 30}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 10, "eMBB": 20, "mMTC": 20}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}, {"allocation": {"URLLC": 10, "eMBB": 30, "mMTC": 10}, "qos": -22.0, "slice_qos": {"URLLC": -10.0, "eMBB": -12.0, "mMTC": 0.0}}]}