# think.md

## 1. 背景与目标
- 本题围绕 5G/未来网络中的 OFDMA、网络切片与异构蜂窝（宏站/微站）资源调度展开，核心是“在多约束下最大化用户服务质量”，并逐步引入干扰、功率控制、接入模式与能耗。
- 参考文件：
  - 题目：`Topic.md`
  - 附录：`Appendix.md`
  - 附件与说明：`附件1_readme.txt`、`附件2_readme.txt`、`附件3_readme.txt`、`附件4_readme.txt`

## 2. 关键建模要素（来自 Appendix.md）
- 信道与速率
  - 接收功率：`p_rx = 10^{(p_{n,k} - φ_{n,k})/10} · |h_{n,k}|^2`（单位注意匹配）
  - 噪声：`N0 = -174 + 10·log10(i·b) + NF`（dBm），b 为单 RB 带宽（附录给出 360 kHz），i 为占用 RB 个数
  - SINR：`γ = p_sig / (p_int + N0)`（Q1/2中通常无或弱干扰，Q3起考虑小区间干扰）
  - 速率：`r = i·b·log(1+γ)`
- 切片与 SLA
  - 三类切片：URLLC / eMBB / mMTC
  - 每类用户每时刻占用的 RB 粒度（表 1）：URLLC=10，eMBB=5，mMTC=2（影响同一时刻可并发服务的数量）
  - SLA（表 1）：速率下限与时延上限（L_SLA）
  - 服务质量（质量函数）按附录定义：
    - URLLC：`y^{URLLC} = { α^L,  L ≤ L_{SLA};  -M^{URLLC},  L > L_{SLA} }`
    - eMBB：分段依赖`r`与`L`
    - mMTC：重在接入比例，且对超时有惩罚
 - 表 1 具体数值（见 `Appendix.md` 表 1）：URLLC 每用户占用 10 RB、eMBB 5 RB、mMTC 2 RB；SLA 速率分别为 10/50/1 Mbps；SLA 时延分别为 5/100/500 ms；惩罚系数 M 依题面给定。
 - 时域：
  - 基本时间片 10 ms（附录）；Q2 中每 100 ms 决策一次（总 1000 ms，10 次决策）；片内可细化为 10 个子时间片进行仿真（便于计算排队与传输完成时延）
 - 频域相邻约束（见 `Appendix.md` 第 2 节）：同一用户的多个 RB、同一切片的 RB 在频域上相邻（不改变总量决策，但影响仿真中的排列与调度）。

## 3. 数据与附件理解（readme 汇总）
- 附件1：单微基站场景的信道与任务量（用户集按 U/e/m 三类）
- 附件2：随时间的信道（含位置）与任务流（1000 ms 内按概率到达）
- 附件3：多微基站 + 干扰（给出各基站信道与任务到达、基站坐标）
- 附件4：宏站+多微站的异构网络 + 任务流（给出信道、基站坐标与任务规模）

## 4. 问题分解与总体方法路线
- Q1：单小区、单时段（或短时）RB 在三类切片之间的划分，片内按固定 RB 粒度为用户服务。目标最大化体现在质量函数求和。
  - 精确可行：在 50 个 RB 的总量约束下，穷举 `(x_URLLC, x_eMBB, x_mMTC)`，并对给定分配仿真片内 FIFO 调度，计算每个任务的 `L=Q+T` 与 `r`，累积质量。
  - 也可用“边际收益贪心”进行近似：每次将 1 个 RB 投入到带来最大质量增益的切片。
- Q2：多时段（10 次决策）+ 随机到达 + 信道随时间变化 + 排队与超时惩罚。
  - 难点来自动态耦合与 SLA 时限。
  - 推荐：MPC（Receding Horizon）。每个 100 ms 决策时，向前滚动若干步（3–5 步）预测任务与信道，解一个短期优化（可用贪心/穷举/混合整数），仅执行第一步，再滚动前进。
  - 备选：Lyapunov Drift-Plus-Penalty（DPP）或 MaxWeight 族策略；或基于近似动态规划/强化学习（如 DQN/PPO）做策略搜索。
- Q3：多微基站 + 干扰 + 功率控制。
  - 结构化分解：
    - 频谱/RB 分配：使用干扰图（共频 RB 的冲突图）做“图着色/分层复用”或“分区复用（FFR）”；
    - 功率控制：WMMSE 或标准迭代功率控制（Yates/Foschini-Miljanic）近似最大化加权速率或能效；
    - 切片 RB 划分与功率控制可交替迭代求解。
- Q4：宏-微异构 + 接入选择（association）+ RB + 功率。
  - 两阶段/层次化：先做带偏置的最大 SINR/最短时延接入（提供初解），再在固定接入下联合优化 RB 划分与功率；外层可重新评估接入并迭代。
- 能耗（第五问）：
  - 以 `P = P_static + P_RB + P_tx`；可进行多目标（最大质量、最小能耗）或能效最大化（质量 / 功耗；或比值/加权和）
  - 典型做法：ε-约束（将能耗作为约束）、加权和、或求 Pareto 前沿。

## 5. 统一仿真与评价框架
- 统一输入：
  - 用户集合及其类别、任务数据量、到达时间、SLA（速率、时延）；
  - 信道时序：`p_{n,k}`（或给出发射功率范围）、`φ_{n,k}`、`|h_{n,k}|^2`，噪声参数；
  - 网络拓扑：基站坐标/小区划分/频谱共享关系。
- 统一过程：
  1) 计算瞬时/平均 `γ` 与 per-RB 速率；
  2) 片内排队与服务（FIFO，编号靠前优先）；
  3) 逐时段更新队列、完成时间与 `L=Q+T`；
  4) 计算质量并汇总；
  5) 记录能耗（在问 5）。
- 评价指标：总质量、三类分别质量、SLA 违约数、吞吐量、平均/95% 时延、能耗/能效。

## 6. Q1 详细设想（摘要）
- 决策：`(x_U, x_E, x_M) ∈ ℕ^3, x_U+x_E+x_M=50`
- 片内并发能力：`cap_U = ⌊x_U/10⌋`, `cap_E = ⌊x_E/5⌋`, `cap_M = ⌊x_M/2⌋`
- 在 10 ms 子帧内对各切片按照并发能力选择等待队首任务服务（编号靠前优先），更新其剩余数据量与完成时间。
- 穷举/贪心得到最佳分配与质量。

## 7. Q2 详细设想（摘要）
- 状态：每 100 ms 时刻的队列向量、剩余数据量、SLA 剩余时限、已观测/预测的信道；
 - 动作：`(x_U(t), x_E(t), x_M(t))`；
 - 约束：`x_U(t)+x_E(t)+x_M(t)=50`（严格等于，见 `Topic.md` 问题二描述）。
- 演化：按 10 个 10 ms 子帧仿真，更新到下一个 100 ms；
- 策略：MPC（预测任务与信道，用穷举/贪心/混合整数在 3–5 步窗口内优化），或 DPP；
- 输出：全局质量最大化与 SLA 违约最小化。

## 8. Q3 详细设想（摘要）
- 场景（见 `Topic.md` 问题三与 `Appendix.md` 第 5 节）：多个微基站共享频谱，存在同频干扰；功率决策范围 `p ∈ [10,30] dBm`，且“每个切片内所有资源块采用统一功率”。
- 时域：同 Q2，1000 ms 内每 100 ms 决策（共 10 次）；每次决策后仿真 10 个 10 ms 子帧。
- 决策（每基站 n、每决策周期 t）：
  - 切片 RB 划分 `x_U^n(t), x_E^n(t), x_M^n(t)`，满足 `x_U^n+x_E^n+x_M^n=50`；
  - 切片功率 `p_U^n(t), p_E^n(t), p_M^n(t) ∈ [10,30] dBm`（切片内 RB 统一功率）。
- 信道与速率：按 `Appendix.md` 第 1 节，SINR 包含其他基站在同一 RB 上的干扰项；速率 `r= i·b·log(1+γ)`，`i` 为该类用户的 RB 粒度（10/5/2）。
- 目标：最大化全网用户质量之和（QoS 定义同第 3 节），约束 RB 总量与功率范围。
- 方法建议：
  - 频谱/RB：基于干扰图做“图着色/分层复用/FFR”以减少共频冲突；
  - 功率：WMMSE 或标准迭代功控（Yates/Foschini-Miljanic）近似最大化加权速率/质量；
  - 交替优化：在固定 RB 下调功率、固定功率下调 RB，迭代至收敛或达到迭代上限。

## 9. Q4 详细设想（摘要）
- 场景（见 `Topic.md` 问题四）：宏-微异构，宏站 100 RB、功率 `p ∈ [10,40] dBm`；多个微站各 50 RB、功率范围同问题三；宏与微频谱不重叠（无互扰），微站之间有同频干扰。
- 决策（每周期 t）：
  - 用户接入/关联 `a_k(t) ∈ {宏站, 微站1..N}`（可带偏置以避免全部接入宏站）；
  - 各基站的切片 RB 划分与切片功率（范围同上），满足每站 RB 上限；
  - 依关联关系在对应基站内进行片内调度（FIFO）。
- 目标：最大化总体用户服务质量（QoS 定义同第 3 节），约束接入、RB、功率与 SLA。
- 方法建议（两阶段/层次化）：
  1) 初始接入：基于最大 SINR/最短时延 + 偏置（例如对宏站加负偏置以均衡负载）；
  2) 在固定接入下联合优化 RB 与功率（可用交替优化/WMMSE/图着色）；
  3) 外层迭代：评估边际收益后更新个别用户接入并重复 2)。

## 10. Q5：能耗优化（详细）
- 目标设置（多种选择，可对比）：
  - 最小化总能耗：`min E_total = \sum_t P(t)\,\Delta t`，满足 QoS/SLA 与资源约束；
  - 加权和：最大化 `\sum QoS - \lambda E_total`，扫描 `\lambda` 得折中曲线；
  - 能效最大化：`max (\sum QoS) / E_total`（可用 Dinkelbach 分式规划）。
- 能耗模型（严格对齐 `Appendix.md` 第 6 节）：
  - 常数：`P_static = 28 W`，`\delta = 0.75 (W/RB)`，`\eta = 0.35`；功率换算 `p_{(W)} = 10^{\frac{p_{(dBm)}-30}{10}}`。
  - 基站功耗：`P(t) = P_{static} + \delta \cdot N_{active}(t) + \sum_k P_{tx,k}(t)/\eta`。
    - 若不做功率控制：`\sum_k P_{tx,k}(t)` 视为常数或上限，能耗主要随“激活 RB 数/时长”变化；
    - 允许休眠：`state(t) \in {ACTIVE, SLEEP}`；SLEEP 时 `P \approx P_{sleep} \ll P_{static}`，切换代价 `C_{sw}`。
  - 激活 RB 数：
    - 切片分配 `x_U,x_E,x_M` 优先按题意取 `=50`；若题面允许节能，可改为 `\le 50`（空闲 RB 省电）；
    - 实际激活 `N_{active}(t)` 可小于上限（队列不足时按需关断）。
  - 变量与约束（在 Q1/Q2 上增补）：
    - 变量：`x_U(t),x_E(t),x_M(t)`；必要时 `P_{tx,k}(t)`、`state(t)`；
    - 约束：SLA（速率/时延/接入比例）、功率上限、RB 上限、队列演化、休眠唤醒时序；
    - 片内调度：仍用 FIFO（编号靠前优先）。
  - 求解思路：
    - 方案 A（无功控，低复杂度）：MPC/贪心 + 阈值法。若“增加 1 RB 的 QoS 边际收益 < 能耗代价”，则不再开启；时间上“压缩服务→留白休眠”。
    - 方案 B（含功控，分式规划）：交替优化切片/RB 与功率；外层 Dinkelbach 或加权和；内层水位/凸近似或启发式。
  - 评价与可视化：
    - 指标：总能耗、能效（QoS/J）、平均功耗、占空比、休眠次数/时长、SLA 违约；
    - 图表：能耗-质量 Pareto 前沿、不同 `\lambda` 的 (x_U,x_E,x_M) 与功率轨迹、占空比热图。
  - 实现注意：
    - 单位统一（W、J、ms）；按子帧累计能耗；
    - 若题面限定必须占满 50 RB，则把节能体现在“更短活跃时长/提前完成后休眠”与“按需关闭未用 RB（分配为上限而非必须占满）”。

## 11. 风险与应对
- 数据字段与单位不一致：先做严格校验（dBm/dB/W、log/ln、Hz/kHz）；
- 规模过大：分解与分层；
- 结果不稳定：多随机种子、多次实验、交叉对比多策略。

## 12. 主要参考（可扩展）
- Shannon (1948) A Mathematical Theory of Communication
- 3GPP TS 38.300（NR 总体；切片参见 SA2/SA5 相关文档）
- Tassiulas & Ephremides (1992) Stability properties of constrained queueing systems
- Neely (2010) Stochastic Network Optimization with Application to Communication and Queueing Systems
- Shi et al. (2011) An Iteratively Weighted MMSE Approach to Distributed Sum-Utility Maximization in Wireless Networks
- Yates (1995) A Framework for Uplink Power Control in Cellular Radio Systems
- Foschini & Miljanic (1993) A Simple Distributed Autonomous Power Control Algorithm and its Convergence
- Proportional Fair Scheduling（经典基站调度方法）

## 13. 下一步
- 落地 Q1/Q2 的求解流程与伪代码；
- 在 `初始思路.md` 中明确可执行步骤；
- 编写解析脚本与仿真骨架（如需）。
