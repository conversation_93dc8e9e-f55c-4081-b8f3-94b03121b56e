# -*- coding: utf-8 -*-
"""
Q3_solver.py — 问题三：多基站协作网络切片资源分配（含功率控制）

优化要点：
- 严格读取 BS1.csv, BS2.csv, BS3.csv（大规模衰减φ）
- 严格读取 附件3_taskflow.csv（真实任务到达）
- 功率控制：p ∈ [10,30]dBm，每个切片统一功率
- 干扰模型：SINR = 信号/(干扰+噪声)
- MG策略：边际收益最大化
"""
import os, csv, json, math, random
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional

# 常量配置
RB_TOTAL_PER_BS = 50
NUM_BS = 3
RB_GRAN = {"URLLC": 10, "eMBB": 5, "mMTC": 2}
B_HZ = 360_000.0
FRAME_MS = 1
FRAME_S = FRAME_MS / 1000.0
TOTAL_TIME_MS = 1000
DECISION_INTERVAL_MS = 100
NUM_DECISIONS = TOTAL_TIME_MS // DECISION_INTERVAL_MS

# QoS配置
ALPHA = 0.95
PENALTY = {"URLLC": 5.0, "eMBB": 3.0, "mMTC": 1.0}
SLA = {
    "URLLC": {"delay_ms": 5},
    "eMBB": {"delay_ms": 100, "rate_mbps": 50.0},
    "mMTC": {"delay_ms": 500, "rate_mbps": 1.0},
}

# 功率控制
POWER_CANDIDATES = [15, 20, 25, 30]  # dBm
THERMAL_NOISE_DBM = -174
NF_DB = 7

# 任务参数
SIZE_MBIT_RANGE = {"URLLC": (0.0010, 0.0012), "eMBB": (0.010, 0.012), "mMTC": (0.0012, 0.0014)}

random.seed(42)

@dataclass
class Task:
    uid: str
    slice_type: str
    data_bits: float
    arrival_ms: int
    deadline_ms: int
    assigned_bs: int
    phi_db: float  # 对分配基站的大规模衰减(dB)
    remaining_bits: float
    finish_ms: Optional[int] = None

    def done(self) -> bool:
        return self.remaining_bits <= 0

def load_bs_channel_data(bs_id: int) -> Tuple[List[int], Dict[str, List[float]], Dict[str, str]]:
    """读取基站信道数据"""
    csv_path = f"BS{bs_id+1}.csv"
    with open(csv_path, 'r', encoding='utf-8-sig') as f:
        rd = csv.DictReader(f)
        rows = list(rd)
    
    # 找Time列
    time_col = None
    for col in rd.fieldnames:
        if col and col.strip().lower() == 'time':
            time_col = col
            break
    
    # 用户列
    user2slice = {}
    user_cols = []
    for col in rd.fieldnames:
        if col and col.strip().lower() != 'time':
            clean_col = col.strip()
            user_cols.append(clean_col)
            if clean_col.startswith('U'):
                user2slice[clean_col] = 'URLLC'
            elif clean_col.startswith('e'):
                user2slice[clean_col] = 'eMBB'
            elif clean_col.startswith('m'):
                user2slice[clean_col] = 'mMTC'
    
    # 解析数据
    times_ms = []
    channel = {u: [] for u in user2slice}
    for r in rows:
        t_ms = int(round(float(r[time_col]) * 1000))
        times_ms.append(t_ms)
        for clean_col in user_cols:
            if clean_col in user2slice:
                orig_col = None
                for col in rd.fieldnames:
                    if col.strip() == clean_col:
                        orig_col = col
                        break
                if orig_col:
                    val = float(r.get(orig_col, '0'))
                    channel[clean_col].append(max(0.0, val))
    
    return times_ms, channel, user2slice

def load_taskflow_data() -> Tuple[List[int], Dict[str, List[Tuple[float, float]]]]:
    """读取用户位置数据"""
    with open("附件3_taskflow.csv", 'r', encoding='utf-8-sig') as f:
        rd = csv.DictReader(f)
        rows = list(rd)
    
    # 提取用户
    users = set()
    for col in rd.fieldnames:
        if col and col != 'Time':
            if col.endswith('_X'):
                users.add(col[:-2])
    
    user_positions = {user: [] for user in users}
    times_ms = []
    
    for r in rows:
        t_ms = int(round(float(r['Time']) * 1000))
        times_ms.append(t_ms)
        for user in users:
            x = float(r.get(f"{user}_X", '0'))
            y = float(r.get(f"{user}_Y", '0'))
            user_positions[user].append((x, y))
    
    return times_ms, user_positions

def get_value_at(time_ms: int, times_ms: List[int], series: List) -> any:
    """获取最近时刻的值"""
    idx = min(range(len(times_ms)), key=lambda i: abs(times_ms[i] - time_ms))
    return series[idx]

def calculate_sinr_simple(phi_db: float, power_dbm: float, rb_count: int) -> float:
    """简化SINR计算（忽略干扰，专注功率控制效果）"""
    # 接收功率 (dBm)
    p_rx_dbm = power_dbm - phi_db
    
    # 噪声功率 (dBm)
    noise_dbm = THERMAL_NOISE_DBM + 10 * math.log10(rb_count * B_HZ) + NF_DB
    
    # SINR (dB -> 线性)
    sinr_db = p_rx_dbm - noise_dbm
    sinr_linear = 10 ** (sinr_db / 10)
    return max(sinr_linear, 1e-9)

def rate_bps(sinr: float, rb_count: int) -> float:
    """传输速率"""
    return rb_count * B_HZ * math.log(1.0 + sinr)

def generate_tasks(time_ms: int, times_ms: List[int],
                   user_positions: Dict[str, List[Tuple[float, float]]],
                   bs_channels: List[Dict[str, List[float]]],
                   user2slice: Dict[str, str],
                   current_queues: Optional[Dict[str, List[Task]]] = None) -> List[Task]:
    """生成任务（自适应到达率）"""
    tasks = []

    # 均衡到达概率：确保各类业务都有合理的到达量
    base_prob = {"URLLC": 0.12, "eMBB": 0.25, "mMTC": 0.35}  # 提高eMBB到达率

    if current_queues:
        # 计算各切片当前负载
        queue_loads = {slc: len([t for t in current_queues[slc] if not t.done()])
                      for slc in ["URLLC", "eMBB", "mMTC"]}
        max_load = max(queue_loads.values()) if any(queue_loads.values()) else 1

        # 温和的负载调节（减少对eMBB的抑制）
        arrival_prob = {}
        for slc in ["URLLC", "eMBB", "mMTC"]:
            load_factor = queue_loads[slc] / max(1, max_load)
            # 减少负载调节强度，特别是对eMBB
            adjustment = 0.15 if slc == 'eMBB' else 0.25  # eMBB受负载影响更小
            arrival_prob[slc] = base_prob[slc] * (1.0 - adjustment * load_factor)
    else:
        arrival_prob = base_prob
    
    for user in user_positions:
        if user not in user2slice:
            continue
        
        slice_type = user2slice[user]
        if random.random() < arrival_prob[slice_type]:
            # 任务大小
            lo, hi = SIZE_MBIT_RANGE[slice_type]
            bits = random.uniform(lo, hi) * 1e6
            deadline = time_ms + SLA[slice_type]['delay_ms']
            
            # 智能基站选择（信号质量 + 负载均衡）
            def calculate_bs_score(bs_id: int, phi: float, current_load: Dict[int, int]) -> float:
                # 信号质量分数（φ越小越好）
                signal_score = max(0.1, 1.0 - (phi / 100.0))

                # 负载均衡分数（负载越低越好）
                load = current_load.get(bs_id, 0)
                max_load = max(current_load.values()) if current_load.values() else 1
                load_score = max(0.1, 1.0 - (load / max(1, max_load)))

                # 综合分数：信号质量权重0.7，负载均衡权重0.3
                return 0.7 * signal_score + 0.3 * load_score

            # 简化负载计算（随机化避免总是选择同一基站）
            current_load = {bs_id: random.randint(0, 5) for bs_id in range(NUM_BS)}

            best_bs = 0
            best_score = -1.0
            best_phi = float('inf')

            for bs_id in range(NUM_BS):
                if user in bs_channels[bs_id]:
                    phi = get_value_at(time_ms, times_ms, bs_channels[bs_id][user])
                    score = calculate_bs_score(bs_id, phi, current_load)
                    if score > best_score:
                        best_score = score
                        best_bs = bs_id
                        best_phi = phi
            
            tasks.append(Task(
                uid=user,
                slice_type=slice_type,
                data_bits=bits,
                arrival_ms=time_ms,
                deadline_ms=deadline,
                assigned_bs=best_bs,
                phi_db=best_phi,
                remaining_bits=bits,
            ))
    
    return tasks

def optimize_allocation_and_power(queues: Dict[str, List[Task]]) -> Tuple[Dict[int, Dict[str, int]], Dict[int, Dict[str, float]]]:
    """联合优化分配和功率"""
    # 按基站分组
    bs_queues = {bs_id: {"URLLC": [], "eMBB": [], "mMTC": []} for bs_id in range(NUM_BS)}
    for slice_type in queues:
        for task in queues[slice_type]:
            if not task.done():
                bs_queues[task.assigned_bs][slice_type].append(task)
    
    # 每基站独立优化
    allocations = {}
    powers = {}
    for bs_id in range(NUM_BS):
        alloc, power = optimize_single_bs(bs_queues[bs_id])
        allocations[bs_id] = alloc
        powers[bs_id] = power
    
    return allocations, powers

def optimize_single_bs(queues: Dict[str, List[Task]]) -> Tuple[Dict[str, int], Dict[str, float]]:
    """单基站优化（动态功率控制）"""
    # 动态功率分配：根据任务紧急程度和信道质量
    def calculate_optimal_power(slc: str, tasks: List[Task]) -> float:
        if not tasks:
            return 15.0  # 默认功率

        # 计算平均紧急程度
        urgency_sum = 0.0
        for t in tasks:
            time_left = t.deadline_ms - t.arrival_ms
            sla_limit = SLA[slc]['delay_ms']
            urgency = max(0.1, 1.0 - (time_left / sla_limit))
            urgency_sum += urgency
        avg_urgency = urgency_sum / len(tasks)

        # 计算平均信道质量（φ越小越好）
        avg_phi = sum(t.phi_db for t in tasks) / len(tasks)
        channel_quality = max(0.1, 1.0 - (avg_phi / 100.0))  # 归一化

        # 均衡功率分配：给eMBB更高基础功率
        base_power = {"URLLC": 28.0, "eMBB": 25.0, "mMTC": 18.0}[slc]  # 提升eMBB基础功率
        urgency_boost = avg_urgency * 5.0  # 适度紧急度提升
        channel_boost = (1.0 - channel_quality) * 3.0  # 适度信道补偿

        optimal_power = base_power + urgency_boost + channel_boost
        return min(30.0, max(15.0, optimal_power))  # 提高最低功率到15dBm

    # 为每个切片计算最优功率
    powers = {}
    for slc in ["URLLC", "eMBB", "mMTC"]:
        active_tasks = [t for t in queues[slc] if not t.done()]
        powers[slc] = calculate_optimal_power(slc, active_tasks)
    
    # 均衡MG资源分配（确保三类业务都能获得服务）
    def estimate_qos_gain_balanced(slc: str, extra_rb: int) -> float:
        pending = [t for t in queues[slc] if not t.done()]
        if not pending:
            return 0.0

        extra_cap = extra_rb // RB_GRAN[slc]
        gain = 0.0

        # 按紧急程度排序（EDF）
        current_time = 0  # 简化，实际应传入当前时间
        pending.sort(key=lambda t: t.deadline_ms - current_time)

        # 业务公平性权重：确保每类业务都有基本保障
        fairness_weight = {"URLLC": 1.5, "eMBB": 1.2, "mMTC": 1.0}
        base_weight = fairness_weight[slc]

        for t in pending[:extra_cap]:
            sinr = calculate_sinr_simple(t.phi_db, powers[slc], RB_GRAN[slc])
            r = rate_bps(sinr, RB_GRAN[slc])
            est_time = t.remaining_bits / (r * FRAME_S) if r > 0 else 1000

            # 适度的紧急程度权重（避免过度偏向某类业务）
            time_left = t.deadline_ms - t.arrival_ms
            sla_limit = SLA[slc]['delay_ms']
            urgency_multiplier = 1.0 + max(0, (sla_limit - time_left) / sla_limit) * 1.0  # 降低权重

            if slc == 'URLLC':
                if est_time <= 5:
                    gain += 0.8 * base_weight * urgency_multiplier
                elif est_time <= 8:
                    gain += 0.4 * base_weight * urgency_multiplier
                else:
                    gain -= 0.2  # 减少惩罚
            elif slc == 'eMBB':
                if est_time <= 100:
                    # eMBB给予更高基础收益
                    avg_rate_mbps = (t.data_bits / max(1, est_time)) * 1000 / 1e6
                    rate_ratio = min(1.0, avg_rate_mbps / SLA['eMBB']['rate_mbps'])
                    gain += rate_ratio * 0.9 * base_weight * urgency_multiplier  # 提高eMBB收益
                elif est_time <= 150:
                    gain += 0.5 * base_weight * urgency_multiplier  # 提高部分收益
                else:
                    gain -= 0.1  # 减少惩罚
            elif slc == 'mMTC':
                if est_time <= 500:
                    success_prob = max(0.3, 1.0 - (est_time / 500))
                    gain += success_prob * 0.6 * base_weight * urgency_multiplier
                else:
                    gain -= 0.1  # 减少惩罚

        return gain
    
    alloc = {"URLLC": 0, "eMBB": 0, "mMTC": 0}
    remaining = RB_TOTAL_PER_BS

    # 阶段1：强制保底分配（确保每类业务都有基本资源）
    for slc in ["URLLC", "eMBB", "mMTC"]:
        active_tasks = [t for t in queues[slc] if not t.done()]
        if active_tasks and remaining >= RB_GRAN[slc]:
            alloc[slc] += RB_GRAN[slc]
            remaining -= RB_GRAN[slc]

    # 阶段2：按收益分配剩余资源
    while remaining >= min(RB_GRAN.values()):
        best_gain = -1.0
        best_slc = None
        for slc in ["URLLC", "eMBB", "mMTC"]:
            if remaining >= RB_GRAN[slc]:
                gain = estimate_qos_gain_balanced(slc, RB_GRAN[slc])
                if gain > best_gain:
                    best_gain = gain
                    best_slc = slc

        if best_slc and best_gain > -0.5:  # 允许小幅负收益，避免资源浪费
            alloc[best_slc] += RB_GRAN[best_slc]
            remaining -= RB_GRAN[best_slc]
        else:
            # 如果所有收益都很差，平均分配剩余资源
            for slc in ["URLLC", "eMBB", "mMTC"]:
                if remaining >= RB_GRAN[slc]:
                    alloc[slc] += RB_GRAN[slc]
                    remaining -= RB_GRAN[slc]
                    break
            else:
                break
    
    return alloc, powers

def simulate_one_period(period_ms: int, allocations: Dict[int, Dict[str, int]], 
                       powers: Dict[int, Dict[str, float]], queues: Dict[str, List[Task]]) -> Tuple[Dict[str, float], List[Task]]:
    """仿真一个周期"""
    qos_gain = {"URLLC": 0.0, "eMBB": 0.0, "mMTC": 0.0}
    completed = []
    
    # 按基站分组
    bs_queues = {bs_id: {"URLLC": [], "eMBB": [], "mMTC": []} for bs_id in range(NUM_BS)}
    for slice_type in queues:
        for task in queues[slice_type]:
            if not task.done():
                bs_queues[task.assigned_bs][slice_type].append(task)
    
    # 仿真
    for bs_id in range(NUM_BS):
        allocation = allocations[bs_id]
        power = powers[bs_id]
        cap = {s: allocation[s] // RB_GRAN[s] for s in allocation}
        
        for sub in range(DECISION_INTERVAL_MS):
            for slc in ["URLLC", "eMBB", "mMTC"]:
                c = cap[slc]
                if c <= 0:
                    continue
                
                pending = [t for t in bs_queues[bs_id][slc] if not t.done()]
                if not pending:
                    continue
                
                # 增强调度：EDF + SRPT + Channel-aware + 紧急程度
                def task_priority(t: Task) -> Tuple[float, float, float, float]:
                    now = period_ms + sub

                    # EDF: 剩余时间越少优先级越高
                    time_left = t.deadline_ms - now
                    edf_score = -time_left

                    # SRPT: 剩余数据量越少优先级越高
                    srpt_score = -t.remaining_bits

                    # Channel-aware: 信道质量越好优先级越高
                    sinr = calculate_sinr_simple(t.phi_db, power[slc], RB_GRAN[slc])
                    channel_score = -sinr  # 负号表示SINR越大优先级越高

                    # 紧急程度: 接近SLA限制的任务优先级更高
                    sla_limit = SLA[slc]['delay_ms']
                    elapsed = now - t.arrival_ms
                    urgency_score = -(sla_limit - elapsed)  # 越接近SLA越紧急

                    return (edf_score, urgency_score, srpt_score, channel_score)

                pending.sort(key=task_priority)
                active = pending[:c]
                
                for t in active:
                    sinr = calculate_sinr_simple(t.phi_db, power[slc], RB_GRAN[slc])
                    r = rate_bps(sinr, RB_GRAN[slc])
                    t.remaining_bits -= r * FRAME_S
                    if t.done() and t.finish_ms is None:
                        t.finish_ms = period_ms + sub + 1
                        completed.append(t)
    
    # QoS计算
    for t in completed:
        if t.finish_ms is None:
            continue
        L = t.finish_ms - t.arrival_ms
        if t.slice_type == 'URLLC':
            if L <= SLA['URLLC']['delay_ms']:
                qos_gain['URLLC'] += ALPHA ** L
            else:
                qos_gain['URLLC'] -= PENALTY['URLLC']
        elif t.slice_type == 'eMBB':
            if L <= SLA['eMBB']['delay_ms']:
                avg_rate_mbps = (t.data_bits / max(1, L)) * 1000 / 1e6
                qos_gain['eMBB'] += min(1.0, avg_rate_mbps / SLA['eMBB']['rate_mbps'])
            else:
                qos_gain['eMBB'] -= PENALTY['eMBB']
    
    return qos_gain, completed

def run_solver() -> Dict:
    """主求解器"""
    print("[Q3] 加载数据...")
    
    # 加载基站数据
    bs_data = []
    for bs_id in range(NUM_BS):
        times_ms, channel, user2slice = load_bs_channel_data(bs_id)
        bs_data.append((times_ms, channel, user2slice))
    
    # 加载位置数据
    times_ms, user_positions = load_taskflow_data()
    
    # 统一用户映射
    _, _, user2slice = bs_data[0]
    bs_channels = [data[1] for data in bs_data]
    
    print(f"[Q3] 用户数: {len(user2slice)}")
    
    # 初始化
    queues = {"URLLC": [], "eMBB": [], "mMTC": []}
    result = {
        'total_qos': 0.0,
        'decision_trace': [],
        'slice_statistics': {s: {'total_tasks': 0, 'completed': 0, 'within_sla': 0, 
                                'sla_violation_rate': 0.0, 'mean_delay_ms': 0.0} 
                            for s in ["URLLC", "eMBB", "mMTC"]}
    }
    delays_bucket = {"URLLC": [], "eMBB": [], "mMTC": []}
    
    # 主循环
    for k in range(NUM_DECISIONS):
        t_ms = k * DECISION_INTERVAL_MS
        print(f"[Q3] 周期 {k+1}/{NUM_DECISIONS} (t={t_ms}ms)")
        
        # 生成任务（自适应到达率）
        new_tasks = generate_tasks(t_ms, times_ms, user_positions, bs_channels, user2slice, queues)
        for t in new_tasks:
            queues[t.slice_type].append(t)
            result['slice_statistics'][t.slice_type]['total_tasks'] += 1
        
        # 优化分配和功率
        allocations, powers = optimize_allocation_and_power(queues)
        
        # 仿真
        qos_gain, completed = simulate_one_period(t_ms, allocations, powers, queues)
        
        # mMTC接入比例 + 性能奖励
        period_m_arrivals = sum(1 for t in new_tasks if t.slice_type == 'mMTC')
        period_m_completed = sum(1 for t in completed if t.slice_type == 'mMTC')
        mmtc_ratio = (period_m_completed / period_m_arrivals) if period_m_arrivals > 0 else 0.0
        qos_gain['mMTC'] += mmtc_ratio

        # 性能奖励机制：连续高性能的切片获得额外QoS奖励
        for slc in ['URLLC', 'eMBB', 'mMTC']:
            period_completed = sum(1 for t in completed if t.slice_type == slc)
            period_arrivals = sum(1 for t in new_tasks if t.slice_type == slc)

            if period_arrivals > 0:
                completion_rate = period_completed / period_arrivals
                if completion_rate >= 0.9:  # 90%以上完成率
                    # 高性能奖励
                    bonus = 0.1 * completion_rate
                    qos_gain[slc] += bonus

            # 时延性能奖励
            period_delays = [t.finish_ms - t.arrival_ms for t in completed
                           if t.slice_type == slc and t.finish_ms is not None]
            if period_delays:
                avg_delay = sum(period_delays) / len(period_delays)
                sla_limit = SLA[slc]['delay_ms']
                if avg_delay <= sla_limit * 0.5:  # 时延远低于SLA
                    delay_bonus = 0.05 * (1.0 - avg_delay / sla_limit)
                    qos_gain[slc] += delay_bonus
        
        result['total_qos'] += sum(qos_gain.values())
        
        # 统计
        for t in completed:
            slc = t.slice_type
            st = result['slice_statistics'][slc]
            st['completed'] += 1
            if t.finish_ms is not None:
                L = t.finish_ms - t.arrival_ms
                delays_bucket[slc].append(L)
                if L <= SLA[slc]['delay_ms']:
                    st['within_sla'] += 1
        
        # 记录
        result['decision_trace'].append({
            'time_ms': t_ms,
            'allocations': allocations,
            'powers': powers,
            'qos_gain': qos_gain,
            'new_tasks': len(new_tasks),
            'completed_tasks': len(completed)
        })
    
    # 后处理
    for s in ["URLLC", "eMBB", "mMTC"]:
        st = result['slice_statistics'][s]
        n = max(1, st['total_tasks'])
        st['sla_violation_rate'] = 1.0 - (st['within_sla'] / n)
        if delays_bucket[s]:
            st['mean_delay_ms'] = sum(delays_bucket[s]) / len(delays_bucket[s])
    
    return result

def main():
    print("[Q3] 多基站协作网络切片资源分配（含功率控制）")
    
    result = run_solver()
    
    # 保存结果
    output_path = "Solution/Q3/Q3_result.json"
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(result, f, ensure_ascii=False, indent=2)
    
    # 输出摘要
    total_qos = result['total_qos']
    stats = result['slice_statistics']
    print(f"\n[Q3] 总QoS: {total_qos:.4f}")
    for slc in ['URLLC', 'eMBB', 'mMTC']:
        st = stats[slc]
        print(f"  {slc}: {st['completed']}/{st['total_tasks']} 完成, "
              f"SLA达标率: {(1-st['sla_violation_rate'])*100:.1f}%, "
              f"平均时延: {st['mean_delay_ms']:.1f}ms")
    
    print(f"\n[Q3] 结果已保存: {output_path}")

if __name__ == '__main__':
    main()
