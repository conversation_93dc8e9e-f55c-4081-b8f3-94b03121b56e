# Q4 讲解

## 1. 问题到底在求什么？
- 场景：一座宏基站（MBS，100 RB）+ 多个微基站（SBS，50 RB/站）。宏/微频谱不重叠，互不干扰；微↔微复用同频，会互相干扰。
- 每 100 ms 决策一次（共 10 次），在每次决策内要同时确定：
  1) 用户接入：该用户由宏还是最近微来服务（二选一）。
  2) 各基站三类切片（URLLC/eMBB/mMTC）的 RB 划分（相邻性约束）。
  3) 各基站、各切片的统一发射功率（宏 10–40 dBm；微 10–30 dBm）。
- 片内：URLLC/eMBB/mMTC 的每用户 RB 粒度为 10/5/2；FIFO 调度；同用户与同切片的 RB 在频域相邻（Appendix 2）。
- 目标：1000 ms 内最大化所有用户的总服务质量（QoS）（Appendix 3 的 SLA/惩罚规则）。

## 2. 入门概念（异构 + 干扰）
- 宏与微：不同频谱→互不干扰。
- 微与微：同频复用→同索引的 RB 互相干扰（见 Appendix 5）。需“跨站 RB 索引对齐”。
- dBm↔W：`P(W)=10^{(P(dBm)-30)/10}`；噪声 `N0=-174+10·log10(i·b)+NF`（dBm），换线性再入 SINR。
- OFDMA 相邻性：同切片的 `x_{b,s}` 个 RB 连续；单用户占用的多个 RB 也连续。

## 3. 我们在“连什么 + 分什么 + 调什么”
- 连（接入）：用户在“宏 vs 最近微”中二选一。
- 分（RB 切片）：每个基站把（100 或 50）RB 划为 3 个连续区段，分别给 U/E/M。
- 调（功率）：给每个基站、每个切片一个统一的 dBm 功率（宏 10–40，微 10–30）。

## 4. 为什么分层/交替？
- 直接联合优化“接入 + RB + 功率”→ 组合 + 非凸，难度高。
- 经验有效路线：时间上用“滚动优化（MPC）或 DPP 在线法”，在单周期内用“交替优化”（先接入+RB，再功率），循环少量轮次即可明显提升。

## 5. 两条主线方法
### 5.1 分层交替优化（推荐）
- 子问题 A（接入 + RB）：固定功率，先给每个用户宏/微的“单位资源效用”估计，做初始接入；再按 10/5/2 的粒度枚举有限“连续切分候选”求最优；对边缘用户做少量“换站”微调。
- 子问题 B（功率）：固定接入与 RB，做功率爬山/投影（或 WMMSE/标准干扰函数法），宏在 [10,40]，微在 [10,30] 内裁剪。
- 每个 100 ms 决策内交替 2~5 轮，执行当期方案，推进 10×10 ms 子帧仿真。

### 5.2 DPP（在线） + 层内微调（工程友好）
- DPP 计算各切片“单位 RB 权重”（与队列/等待时间/接近 SLA 程度相关），逐个 RB 贪心分配给权重最大的基站-切片；
- 周期内穿插少量功率步进更新；
- 对少量用户尝试“宏↔微”切换，若 QoS 提升则接受。

## 6. 计算细节与易错点
- 单位换算：功率、噪声必须转线性后再带入 `log(1+γ)`；`b=360 kHz`；i 为每类固定粒度（10/5/2）。
- 干扰对齐：微↔微的干扰只在“相同 RB 索引”上产生；跨站必须统一索引。
- 相邻性：同切片/同用户 RB 连续；三个切片区段在各站内不重叠。
- 接入限制：仅在“宏 vs 最近微”之间切换，避免组合爆炸。
- SLA 判定：等号属于“满足侧”（与 Appendix 约定一致）。

## 7. 输出与可视化
- 输出：每周期的接入决策，宏/微各基站 `(x_{*,U},x_{*,E},x_{*,M})` 与 `(p_{*,U},p_{*,E},p_{*,M})`，总 QoS、SLA 违约、平均/95% 时延。
- 图形：
  - 接入热力图（宏覆盖 vs 微分担）；
  - QoS 与功率随时间的曲线；
  - 微↔微干扰热图；
  - 与基线（固定接入/固定功率）对比。

## 8. 一个可落地的起步路线
1) 先实现“固定接入（最近微）、固定功率”的基线，打通仿真。
2) 加入“接入 + RB”交替（有限候选），再加入功率步进更新。
3) 切到 MPC 窗口（H=3~5）或 DPP 在线，做 10 次滚动。
4) 做灵敏度分析：窗口 H、功率步长、候选数量、换站阈值、随机种子。

## 9. 写作建议（报告）
- 绘制系统/干扰/接入示意图；
- 逐条给变量、约束、目标；
- 附上算法流程图/伪代码；
- 展示对比与灵敏度分析，解释“为什么方案更好”。
